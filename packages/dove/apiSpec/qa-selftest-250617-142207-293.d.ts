/**
 * 沟通任务发起-商家列表
 * @namespace qa_selftest_250617_142207_293Types
 * @time 2025-07-07 11:04:29
 * 该文件由 @mfe/cc-api-caller 自动生成
 */

export type qa_selftest_250617_142207_293Types = {
    /**
     * 接口 [商家子任务列表筛选分页展示↗](https://f1-better.sankuai.com/#/home?projectId=1295&api_project_id=105288)的 **请求类型**
     * 更新时间：2025-07-08 11:52:33
     */
    '/xianfu/api-v2/dove/data/poi/subtask': {
        method: 'POST';
        request: {
            /** 页码 */
            page?: number;
            /** 商家 ID, 必填 */
            poiId?: string;
            /** 商家类型，必填 */
            poiType?: number;
            /** 每页数据 */
            pageSize?: number;
            /** 排序 */
            sortList?: {
                /** 排序方向 */
                asc?: boolean;
                /** 排序字段 */
                field?: string;
            }[];
            /** 子任务过滤条件 */
            subtaskFilter?: {
                /** 业务线 ID, 必填 */
                bizId?: number;
                /** 任务 ID，非必填，精确搜索 */
                taskId?: string;
                /** 任务名称，非必填，模糊搜索 */
                taskName?: string;
                /** agent 名称，非必填，模糊搜索 */
                agentName?: string;
                /** 创建人 mis */
                creatorMis?: string;
                /** agentId，非必填，支持多个 agent 一起搜索 */
                agentIdList?: string[];
                /** 触达类型，非必填 */
                contactType?: number;
                /** 创建时间最大值 */
                createTimeMax?: number;
                /** 创建时间最小值 */
                createTimeMin?: number;
                /** 子任务状态，非必填 */
                subTaskStatus?: number;
                /** 子任务触达时间最大值 */
                subTaskStartTimeMax?: number;
                /** 子任务触达时间最小值 */
                subTaskStartTimeMin?: number;
            };
        };
        response: {
            /** 数据 */
            data?: {
                /** 子任务 ID */
                id?: string;
                /** 被叫号码 */
                tel?: string;
                /** 沟通意向 */
                rank?: string;
                /** 业务 ID */
                bizId?: number;
                /** 任务执行状态 */
                status?: string;
                /** 主任务 ID */
                taskId?: string;
                /** 关联 agent ID */
                agentId?: string;
                /** 业务名称 */
                bizName?: string;
                /** 创建人 */
                creator?: string;
                /** 沟通总结 */
                summary?: string;
                /** 沟通时长 */
                duration?: number;
                /** 任务名称 */
                taskName?: string;
                /** 关联 agent 名称 */
                agentName?: string;
                /** 跟进人 mis */
                followMis?: string;
                /** 跟进人 uid */
                followUid?: string;
                /** 调度流程 ID */
                processId?: string;
                /** 触达时间 */
                reachTime?: number;
                /** 创建时间 */
                createTime?: number;
                /** 任务流程类型 */
                contactType?: string;
                /** 调度流程名称 */
                processName?: string;
                /** 触达结果 */
                reachStatus?: string;
                /** 失败原因 */
                failureReason?: string;
            }[];
            /** 页码 */
            page?: number;
            /** 总数 */
            total?: number;
            /** 每页数据 */
            pageSize?: number;
        };
    };
    /**
     * 接口 [商家列表筛选分页展示↗](https://f1-better.sankuai.com/#/home?projectId=1295&api_project_id=105290)的 **请求类型**
     * 更新时间：2025-07-08 11:52:33
     */
    '/xianfu/api-v2/dove/data/poi/search': {
        method: 'POST';
        request: {
            /** 页码 */
            page?: number;
            /** 商家 ID, 非必填，精确搜索 */
            poiId?: string;
            /** 所属城市 ID，非必填 */
            cityId?: number;
            /** 商家名称, 非必填，模糊搜索 */
            poiName?: string;
            /** 商家类型，非必填 */
            poiType?: number;
            /** 每页数据 */
            pageSize?: number;
            /** 排序 */
            sortList?: {
                /** 排序方向 */
                asc?: boolean;
                /** 排序字段 */
                field?: string;
            }[];
            /** 门店责任人 / 认领人, 非必填，同时搜索外卖门店及公海门店 */
            assigneeList?: string[];
            /** 子任务过滤条件 */
            subtaskFilter?: {
                /** 业务线 ID, 必填 */
                bizId?: number;
                /** 任务 ID，非必填，精确搜索 */
                taskId?: string;
                /** 任务名称，非必填，模糊搜索 */
                taskName?: string;
                /** agent 名称，非必填，模糊搜索 */
                agentName?: string;
                /** 创建人 mis */
                creatorMis?: string;
                /** agentId，非必填，支持多个 agent 一起搜索 */
                agentIdList?: string[];
                /** 触达类型，非必填 */
                contactType?: number;
                /** 创建时间最大值 */
                createTimeMax?: number;
                /** 创建时间最小值 */
                createTimeMin?: number;
                /** 子任务状态，非必填 */
                subTaskStatus?: number;
                /** 子任务触达时间最大值 */
                subTaskStartTimeMax?: number;
                /** 子任务触达时间最小值 */
                subTaskStartTimeMin?: number;
            };
        };
        response: {
            /** 数据 */
            data?: {
                /** 门店 ID */
                id?: string;
                /** 所属蜂窝 */
                aor?: string;
                /** 所属城市 */
                city?: string;
                /** 门店名称 */
                name?: string;
                /** 门店类型 */
                type?: string;
                /** 门店地址 */
                address?: string;
                /** 门店 logo */
                logoUrl?: string;
                /** 门店责任人，已合作门店字段 */
                assignee?: string;
                /** 门店三级品类 */
                category?: string;
                /** 认领人, 公海门店字段 */
                claimedBy?: string;
                /** 认领状态，公海门店字段 */
                claimStatus?: string;
                /** 门店上单状态，已合作门店字段 */
                listingStatus?: string;
                /** 营业状态，已合作门店字段 */
                operatingStatus?: string;
            }[];
            /** 页码 */
            page?: number;
            /** 总数 */
            total?: number;
            /** 每页数据 */
            pageSize?: number;
        };
    };
    /**
     * 接口 [子任务统计信息查询↗](https://f1-better.sankuai.com/#/home?projectId=1295&api_project_id=105455)的 **请求类型**
     * 更新时间：2025-07-09 10:30:19
     */
    '/xianfu/api-v2/dove/data/poi/subtask/statistics': {
        method: 'POST';
        request: {
            /** 页码 */
            page?: number;
            /** 商家 ID, 非必填，精确搜索 */
            poiId?: string;
            /** 所属城市 ID，非必填 */
            cityId?: number;
            /** 商家名称, 非必填，模糊搜索 */
            poiName?: string;
            /** 商家类型，非必填 */
            poiType?: number;
            /** 每页数据 */
            pageSize?: number;
            /** 排序 */
            sortList?: {
                /** 排序方向 */
                asc?: boolean;
                /** 排序字段 */
                field?: string;
            }[];
            /** 门店责任人 / 认领人, 非必填，同时搜索外卖门店及公海门店 */
            assigneeList?: string[];
            /** 子任务过滤条件 */
            subtaskFilter?: {
                /** 业务线 ID, 必填 */
                bizId?: number;
                /** 任务 ID，非必填，精确搜索 */
                taskId?: string;
                /** 任务名称，非必填，模糊搜索 */
                taskName?: string;
                /** agent 名称，非必填，模糊搜索 */
                agentName?: string;
                /** 创建人 mis */
                creatorMis?: string;
                /** agentId，非必填，支持多个 agent 一起搜索 */
                agentIdList?: string[];
                /** 触达类型，非必填 */
                contactType?: number;
                /** 创建时间最大值 */
                createTimeMax?: number;
                /** 创建时间最小值 */
                createTimeMin?: number;
                /** 子任务状态，非必填 */
                subTaskStatus?: number;
                /** 子任务触达时间最大值 */
                subTaskStartTimeMax?: number;
                /** 子任务触达时间最小值 */
                subTaskStartTimeMin?: number;
            };
        };
        response: {
            /** 任务总数 */
            totalCount?: number;
            /** BD 节点数量 */
            bdNodeCount?: number;
            /** AI 节点执行中数量 */
            aiNodeRunningCount?: number;
            /** AI 节点已结束数量 */
            aiNodeCompletedCount?: number;
            /** 电销节点执行中数量 */
            teleSalesNodeRunningCount?: number;
            /** 电销节点已结束数量 */
            teleSalesNodeCompletedCount?: number;
        };
    };
};
