module.exports = {
    path: './apiSpec',
    errorCode: {
        FAILED: 1,
        // 参数错误
        ILLEGAL_ARG: 700001,
        // 数据错误
        DATA_ERROR: 800001,
        // 系统错误
        SYSTEM_ERROR: 900001,
    },
    blacklist: [/^\/bee/],
    f1api: {
        projectList: [
            {
                projectId: 1200, //1295
                foldIds: ['1200_2025/06/12_11:33:58:845_17629'],
            },
            {
                projectId: 1295,
            },
        ],
        openApiRequestMis: 'huangfengli02',
    },
    // papi: {
    //     projectList: [
    //         {
    //             projectId: 'de881c40-47ce-462a-9787-8ab230850f18',
    //             categoryList: ['沟通agent', 'BD数字分身'],
    //         },
    //     ],
    // },
    // yapi: {
    //     blacklist: [/^\/crm/],
    //     projects: [
    //         {
    //             token: '7e3769901ee9214e96728ce0ed878716e9699ef80ab42aae4f9e19ff76653315',
    //             categoryList: [
    //                 {
    //                     id: 213071, // yapi链接：/interface/api/cat_400480， 取cat_后面的数字
    //                     name: 'pc', // yapi的分类的名字
    //                     alias: 'themeChat',
    //                 },
    //             ],
    //         },
    //     ],
    // },
};
