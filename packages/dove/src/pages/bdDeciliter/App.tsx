import React, { useState, useEffect, useContext } from 'react';
import DialogFlow from './common/components/DialogFlow';
import './index.scss';
import { Button } from '@roo/roo';
import { Spin, Modal, Select } from 'antd';
import { FormPro } from '@roo/roo';
import { Toast } from '@roo/roo';

import {
    addTemplate,
    updateTemplate,
    getTemplateById,
    publishAgent,
    getDefaultInitialValue,
} from './services';
// 导入类型和工具函数
import {
    ensureCorrectLineBreaks,
    processFormData,
    convertInputParamsToObject,
    getOcrmHost,
    getEnvironment,
    Environment,
} from './common/utils';

import { AgentTypeEnum } from '@src/constants';
import BDDeciliterProvider, { BDDeciliterContext } from './context';

import HeadquartersForm from './modules/headquarters/pages/index';
import FrontLineForm from './modules/frontLine/pages';

interface BizOption {
    label: string;
    value: number;
}

interface BizListResponse {
    code: number;
    data: BizOption[];
}

// 定义一个简单的getBizList函数来模拟数据获取
const getBizList = async (): Promise<BizListResponse> => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve({
                code: 0,
                data: [
                    { label: '外卖业务', value: 1 },
                    { label: '到店业务', value: 2 },
                    { label: '其他业务', value: 3 },
                ],
            });
        }, 500);
    });
};
// 定义默认的意向分析Prompt文本
export const DEFAULT_INTENTION_PROMPT = `给你是一位对话分析专家，你需要依据对话信息和对话内容来确定被叫方的意向。请根据对话信息和对话内容确定被叫方的意向，严格输出一个意向标签名称，如果你觉得都不合适，就输出other。
以下是完整对话内容:
#{contact_text_messages}
被叫方的可能意向如下（其中tagName是意向标签的名称，tagDescription是意向标签的描述）:
#{intention_tags}
请注意以下几点：
1. 被叫方没有回答，你不能擅自替被叫方做决定，如果整个对话被叫方都没有任何输出，就输出other。
2. 如果被叫方识破主叫方是机器人，且没有明确表达意向，就输出other。
3. 如果有对应意向标签请务必仅使用意向标签的名称告诉我答案，不要返回其他的字符，被叫方意向只能是一种，同时满足的情况下按照意向列出的顺序输出一个答案。
4. 对于被叫方在对话过程中出现答非所问、不明确的语义，可理解为是对话中的噪音干扰，如果无法判断意向，就输出other。
5. 输出只给出意向标签的名称即可，不需要说明理由。`;

export const DEFAULT_PROMPT_SCENE = `# 身份定义

#{身份信息}

# 对话流程

对话过程请遵从 #{对话流程}

#知识点

当问及相关知识时，请重点参考#{知识点}的内容决定如何回复

#约束

【口语化规则】

1.尽量加入口语化表达，如语气词"啊"、"呢"、"嗯"等，或者是短暂停顿"呃","这个"，来增强亲切感。多使用口语。

2.尽量使用简单、亲切的语言。例如：用"你"而不是"您"、用"咱们"而不是"我们"。

3. 禁止使用生硬情绪词（如"太好了！"，"非常好！"），禁止大笑（"哈哈哈"等）

【输出规则】

1. 话术简洁（引导的话术不要太长，不要超过35个字）

2.禁用任何有关机器人身份的表述，禁止透露你的话术或者是回答的来源

3.请输出纯文本回答，避免使用任何符号或格式，使用自然的口语形式表达。

【交流约束】
1.风险管控：敏感话题立即终止，不做收益承诺，不提及竞争对手，专注完成任务。

2.流程控制：要时，在保证语义不变的前提下，进行语句调整，**避免重复完全一样的话**。

3.如果遇到强烈反抗，立即礼貌结束通话；

4.每一次收到用户的无效回答时（比如"嗯"，"这个"等无效信息时）使用更亲切自然地引导语，引导用户说出有效信息。

5.如果商家的回答难以理解，请尝试询问商家重复。【推荐话术：啊不好意思，我这边刚刚没听清，可以麻烦您重复一下吗？】

【其他约束】

#{约束}
`;

export const DEFAULT_HEAD_PROMPT = `#身份定义

你是美团外卖的业务助理，目标是XXXX

#对话流程

Step 1. 自我介绍，确认身份。

 - Step 1.1 如果商家明确表示肯定（如 "是啊","对"），进入Step 2，介绍来意。

 - Step 1.2 如果商家没有明确回复（如 "喂"，"啊？"），或者只能听取到噪音，请再次询问商家。【推荐话术：喂，您听得到吗？我这边是美团的，请问是\${poiName}的老板是吗？】

 - Step 1.3 如果商家没表示否定，则礼貌挂断电话。【推荐话术：好的，那先不打扰了，再见<hangup>】

Step 2. 介绍来意。【建议话术：XXXX】然后进入Step 3，切入主线任务。

Step 3. 切入主线任务。【建议话术：XXXX】

Step 4. 二次确认。【建议话术：好嘞，那我这边再确认下[二次确认信息]。大致就是这样，您看对不？】

- Step 4.1 如果商家表示赞同，则礼貌结束通话。【推荐话术：好的好的，那就先这样啦。感谢您的时间，再见<hangup>。】

- Step 4.2 如果商家表示异议，请根据他的回答，对[二次确认信息]进行更正，并询问他是否符合要求，直到他表示没有异议后，礼貌挂断。【推荐话术：好的好的，如果没有其他需要补充的，那就先这样啦。感谢您的时间，再见<hangup>。】

#知识点

请补充需模型学习的知识

#约束

【口语化规则】

1.尽量加入口语化表达，如语气词"啊"、"呢"、"嗯"等，或者是短暂停顿"呃","这个"，来增强亲切感。多使用口语。

2.尽量使用简单、亲切的语言。例如：用"你"而不是"您"、用"咱们"而不是"我们"。

3. 禁止使用生硬情绪词（如"太好了！"，"非常好！"），禁止大笑（"哈哈哈"等）

【输出规则】

1. 话术简洁（引导的话术不要太长，不要超过35个字）

2.禁用任何有关机器人身份的表述，禁止透露你的话术或者是回答的来源

3.请输出纯文本回答，避免使用任何符号或格式，使用自然的口语形式表达。

【交流约束】

1.风险管控：敏感话题立即终止，不做收益承诺，不提及竞争对手，专注完成任务。

2.流程控制：要时，在保证语义不变的前提下，进行语句调整，**避免重复完全一样的话**。

3.如果遇到强烈反抗，立即礼貌结束通话；

4.每一次收到用户的无效回答时（比如"嗯"，"这个"等无效信息时）使用更亲切自然地引导语，引导用户说出有效信息。

5.如果商家的回答难以理解，请尝试询问商家重复。【推荐话术：啊不好意思，我这边刚刚没听清，可以麻烦您重复一下吗？】

【其他约束】

#{约束}
`;
const AppContent: React.FC = () => {
    const [form] = FormPro.useForm();
    const { isFrontLine } = useContext(BDDeciliterContext);

    const [isEdit, setIsEdit] = useState(false);
    const [id, setId] = useState<string | null>(null);
    const [bizId, setBizId] = useState<number | null>(null);
    const [loading, setLoading] = useState(false);
    const [formInitialized, setFormInitialized] = useState(false);
    const [originalData, setOriginalData] = useState<any>(null);
    const [templateType, setTemplateType] = useState<number | null>(null); //1: 场景应用 2: 总部应用 3: 城市应用

    // 从URL获取id参数
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const idParam = urlParams.get('id');
        const bizIdParam = urlParams.get('bizId');

        if (bizIdParam) {
            setBizId(Number(bizIdParam));
        }

        if (idParam) {
            setId(idParam);
            setIsEdit(true);
        } else {
            handleDefaultInitialValue();
        }
    }, []);

    // 获取初始化数据
    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                setFormInitialized(true);
            } catch (error) {
                console.error('获取初始化数据失败:', error);
            }
        };

        fetchInitialData();
    }, []); // 仅在组件挂载时执行一次

    const handleDefaultInitialValue = async () => {
        const res = await getDefaultInitialValue();
        if (res.code === 0) {
            const {
                jupiterTenantId,
                llmAppId,
                routePoint,
                llmName = 'Doubao-1.5-lite-32k',
                intentionLlmName,
            } = res.data as any;

            form.setFieldsValue({
                agentChannel: [
                    {
                        jupiterTenantId,
                        routePoint,
                    },
                ],
                llmAppId,
                llmName,
            });
            form.setFieldValue(['intention', 'llmAppId'], llmAppId);
            form.setFieldValue(['intention', 'llmName'], intentionLlmName);
            form.setFieldValue(['modelConfig', 'maxRecall'], '5');
        }
    };

    const handleSave = async () => {
        try {
            // 获取表单数据
            const values = await form.validateFields();
            console.log('原始表单数据:', values);

            // 移除可能存在的独立字段
            const { dialogFlow, knowledgePoints, constraints, ...formData } =
                values;

            // 构建提交数据，使用深拷贝避免引用问题
            const submitData = originalData
                ? JSON.parse(JSON.stringify(originalData))
                : {};

            // 确保bizId被添加到提交数据中
            submitData.bizId = bizId;

            // 需要更新的字段列表
            const updatedFields = [
                'name',
                'description',
                'agentChannel',
                'bizId', // 确保bizId在更新字段列表中
                'objectType',
                'status',
                'auth',
                'placeholder',
                'groupId',
                'prologue',
                'llmName',
                'llmAppId',
                'type',
                'sceneName',
                'systemPrompt',
                'skill',
                'knowledgeBase',
                'version',
                'intention',
                'objectName',
                'modelConfig',
                'keywordExtractConfig',
            ];

            // 针对表单中存在的字段，从表单数据更新到提交数据
            updatedFields.forEach(field => {
                if (field === 'modelConfig') {
                    if (formData[field] === null) {
                        formData[field] = {};
                    }
                }
                if (formData[field] !== undefined) {
                    // 特殊处理agentChannel字段，确保保持数组类型
                    if (field === 'agentChannel') {
                        // 如果原始数据中不存在，初始化为空数组
                        if (!submitData.agentChannel) {
                            submitData.agentChannel = [];
                        }

                        // 确保formData中的agentChannel也是数组类型
                        const formAgentChannel = Array.isArray(
                            formData.agentChannel,
                        )
                            ? formData.agentChannel
                            : [formData.agentChannel];

                        // 遍历表单中的agentChannel数组
                        formAgentChannel.forEach((channel, index) => {
                            if (!submitData.agentChannel[index]) {
                                submitData.agentChannel[index] = {};
                            }

                            // 合并原始数据和表单数据
                            submitData.agentChannel[index] = {
                                ...submitData.agentChannel[index],
                                ...channel,
                            };
                        });

                        console.log(
                            '处理后的agentChannel:',
                            submitData.agentChannel,
                        );
                    }
                    // 处理systemPrompt字段
                    else if (field === 'systemPrompt') {
                        if (!submitData.systemPrompt) {
                            submitData.systemPrompt = {};
                        }

                        // 合并systemPrompt的非inputParams字段
                        Object.entries(formData.systemPrompt || {}).forEach(
                            ([key, value]) => {
                                if (key !== 'inputParams') {
                                    submitData.systemPrompt[key] = value;
                                }
                            },
                        );

                        // inputParams将在后面单独处理
                    }
                    // 处理其他对象类型字段
                    else if (
                        typeof formData[field] === 'object' &&
                        formData[field] !== null
                    ) {
                        // 检查是否是数组类型
                        if (Array.isArray(formData[field])) {
                            // 如果是数组，确保目标也是数组
                            if (!Array.isArray(submitData[field])) {
                                submitData[field] = [];
                            }

                            // 合并数组元素
                            formData[field].forEach((item, index) => {
                                if (typeof item === 'object' && item !== null) {
                                    // 对象类型元素，进行属性合并
                                    submitData[field][index] = {
                                        ...(submitData[field][index] || {}),
                                        ...item,
                                    };
                                } else {
                                    // 非对象类型元素，直接替换
                                    submitData[field][index] = item;
                                }
                            });
                        } else {
                            // 普通对象类型，合并属性
                            submitData[field] = {
                                ...(submitData[field] || {}),
                                ...formData[field],
                            };
                        }
                    } else {
                        // 非对象类型直接赋值
                        submitData[field] = formData[field];
                    }
                }
            });

            // 特殊处理 systemPrompt.inputParams
            if (submitData.systemPrompt) {
                try {
                    // 先保存原始 inputParams
                    const originalInputParams = Array.isArray(
                        submitData.systemPrompt.inputParams,
                    )
                        ? submitData.systemPrompt.inputParams
                        : [];

                    // 创建一个映射，用于快速查找原始数据中的项
                    const originalParamsMap = {};
                    originalInputParams.forEach(item => {
                        if (item && item.name) {
                            originalParamsMap[item.name] = item;
                        }
                    });

                    // 处理每个字段
                    const processedInputParams: any = [];

                    // 处理表单中的 inputParams（此时可能是对象格式）
                    const formInputParams =
                        formData.systemPrompt?.inputParams || {};

                    // 合并表单中的数据和原始数据
                    Object.entries(formInputParams).forEach(
                        ([key, value]: [string, any]) => {
                            if (!value) return;

                            const needArrayToString = [
                                '对话流程',
                                '知识点',
                                '约束',
                            ].includes(key);

                            // 获取原始数据中的项（如果存在）
                            const originalItem = originalParamsMap[key];

                            // 保留原有的布尔值字段，优先使用表单中的值，如果没有则使用原始数据中的值
                            const requiredFrontLineChange =
                                value.requiredFrontLineChange !== undefined
                                    ? value.requiredFrontLineChange
                                    : originalItem?.requiredFrontLineChange ??
                                      true;

                            const allowFrontLineChange =
                                value.allowFrontLineChange !== undefined
                                    ? value.allowFrontLineChange
                                    : originalItem?.allowFrontLineChange ??
                                      true;

                            // 处理content字段
                            let content = value.content;

                            if (needArrayToString && Array.isArray(content)) {
                                content = content.map(item => {
                                    const _item = item.replace(/\n/g, '\t');
                                    return _item;
                                });
                                // 将数组转换为字符串
                                content = content.join('\n');
                                // 确保换行符格式正确
                                content = ensureCorrectLineBreaks(content);
                            }

                            // 跳过content为空的数据
                            if (
                                content === undefined ||
                                content === null ||
                                content === '' ||
                                (Array.isArray(content) && content.length === 0)
                            ) {
                                return;
                            }

                            // 添加到数组中，合并表单数据和原始数据
                            processedInputParams.push({
                                // 保留原始数据中的其他字段
                                ...(originalItem || {}),
                                // 使用最新值覆盖
                                name: key,
                                content,
                                requiredFrontLineChange,
                                allowFrontLineChange,
                            });
                        },
                    );

                    // 添加表单中没有但原始数据中存在的字段
                    originalInputParams.forEach(item => {
                        if (
                            item &&
                            item.name &&
                            !formInputParams[item.name] &&
                            item.content !== undefined &&
                            item.content !== null &&
                            item.content !== '' &&
                            !(
                                Array.isArray(item.content) &&
                                item.content.length === 0
                            )
                        ) {
                            processedInputParams.push(item);
                        }
                    });

                    // 更新提交数据中的inputParams为数组
                    submitData.systemPrompt.inputParams = processedInputParams;

                    console.log('处理后的inputParams:', processedInputParams);
                } catch (error) {
                    console.error('处理content字段时出错:', error);
                }
            }

            // 确保agentChannel字段的完整性
            if (!submitData.agentChannel) {
                submitData.agentChannel = [];
            }

            if (!submitData.agentChannel[0]) {
                submitData.agentChannel[0] = {};
            }

            // 确保agentChannel[0]包含所有必要字段
            submitData.agentChannel[0] = {
                type: 2,
                botId: '',
                ...(originalData?.agentChannel?.[0] || {}), // 保留原始数据
                ...submitData.agentChannel[0], // 新数据覆盖
            };

            if (isFrontLine && !id) {
                // 一线bd智能新增城市模板 设置默认值
                submitData.type = 3;
                submitData.auth = {
                    type: 2,
                    org: [],
                };
            }
            if (!submitData.bizId) {
                submitData.bizId = bizId;
            }

            // 如果是总部应用(type=2)，删除场景名称和参数配置
            if (submitData.type === 2) {
                // 删除场景名称
                delete submitData.sceneName;

                // 清空参数配置
                if (
                    submitData.systemPrompt &&
                    submitData.systemPrompt.inputParams
                ) {
                    submitData.systemPrompt.inputParams = [];
                }
            }
            console.log('最终提交的数据:', submitData);

            // 根据是否是编辑模式调用不同的API
            let isSuccess = false;
            let templateId = id;
            try {
                // BD视角添加id
                if (isFrontLine || templateType === AgentTypeEnum.CITY) {
                    submitData.parentAgentId = originalData.parentAgentId;
                }
                if (isEdit && id) {
                    // 编辑模式 - 调用更新接口
                    const res: any = await updateTemplate(id, submitData);
                    if (res.code === 0) {
                        Toast.success('更新成功');
                        isSuccess = true;
                    } else {
                        Toast.fail('更新失败');
                        isSuccess = false;
                    }
                } else {
                    // 新增模式 - 调用创建接口
                    const result = await addTemplate(submitData);
                    if (result.success) {
                        templateId = result?.data;
                        isSuccess = true;
                        Toast.success('创建成功');
                    } else {
                        Toast.fail('创建失败');
                    }

                    // 新增模式下保存成功后，更新URL并设置为编辑模式
                    if (templateId) {
                        setId(templateId);
                        setIsEdit(true);
                        // 更新URL，但不刷新页面
                        window.history.pushState(
                            {},
                            '',
                            `${window.location.pathname}?id=${templateId}`,
                        );
                    }
                }

                // 保存/更新成功后重新获取数据
                if (templateId && isSuccess) {
                    setLoading(true);
                    try {
                        const res = await getTemplateById(templateId);
                        if (res) {
                            // 处理数据
                            const processedData = processFormData(res);

                            // 将inputParams从数组转换为对象格式
                            if (
                                processedData.systemPrompt &&
                                Array.isArray(
                                    processedData.systemPrompt.inputParams,
                                )
                            ) {
                                const inputParamsObj =
                                    convertInputParamsToObject(
                                        processedData.systemPrompt.inputParams,
                                    );
                                processedData.systemPrompt.inputParams =
                                    inputParamsObj as any;
                            }

                            // 设置表单值
                            setFormDataAndValidate(processedData);
                        }
                    } catch (error) {
                        console.error('重新获取模板详情失败:', error);
                        Toast.fail('重新获取数据失败，请手动刷新页面');

                        return null;
                    } finally {
                        setLoading(false);
                    }
                }
            } catch (error) {
                console.error('保存数据时出错:', error);
                Toast.fail('保存失败，请稍后重试');
            }

            return templateId;
        } catch (error) {
            console.error('表单验证失败:', error);
            if (error && (error as any).errorFields) {
                const errorFields = (error as any).errorFields as any[];
                const hasIntentionValidFail = errorFields.some(item => {
                    const { name } = item;
                    if (Array.isArray(name) && name.includes('intention')) {
                        return true;
                    }
                    return false;
                });
                if (hasIntentionValidFail) {
                    return Toast.fail('请完成意向配置');
                }
            }

            Toast.fail('表单验证失败，请检查必填项');
        }
    };

    // 设置表单数据并触发校验
    const setFormDataAndValidate = (data: any) => {
        // 保存原始数据
        setOriginalData(data);

        // 处理 inputParams 中的 content 字段
        if (data.systemPrompt?.inputParams) {
            Object.entries(data.systemPrompt.inputParams).forEach(
                ([key, value]: [string, any]) => {
                    if (!value) return;

                    const needStringToArray = [
                        '对话流程',
                        '知识点',
                        '约束',
                    ].includes(key);

                    if (
                        needStringToArray &&
                        typeof value.content === 'string'
                    ) {
                        // 将字符串转换为数组
                        value.content = value.content
                            .split('\n')
                            .map(item => item.trim())
                            .filter(Boolean);
                    }
                },
            );
        }

        form.setFieldsValue(data);

        // 打印设置后的表单值
        console.log(
            '设置后的表单值 - intention:',
            form.getFieldValue('intention'),
        );
        console.log(
            '设置后的 allowFrontLineChange:',
            form.getFieldValue(['intention', 'allowFrontLineChange']),
        );
    };

    // 添加新的useEffect，在表单初始化完成后检查并设置默认值
    useEffect(() => {
        if (formInitialized && !id) {
            // 只有在新增模式（没有id）且表单初始化完成后，才设置默认值

            // 默认值配置
            const defaultValues: any = {
                intention: {
                    systemPrompt: DEFAULT_INTENTION_PROMPT,
                },
                type: isFrontLine ? 3 : 2,
            };

            // 如果是场景应用(type=1)且不是一线BD，则设置额外的默认值
            if (!isFrontLine) {
                // 设置开场白默认值
                defaultValues.prologue =
                    '。。。 哎，老板您好，我这边美团的业务助理，呃...是@{外卖商家名称}的老板是吗？';

                // 设置身份定义默认值
                defaultValues.systemPrompt = {
                    ...defaultValues.systemPrompt,
                    inputParams: {
                        身份信息: {
                            name: '身份信息',
                            content: '你是美团外卖的业务助理，目标是XXXX',
                            allowFrontLineChange: true,
                            requiredFrontLineChange: true,
                        },
                        对话流程: {
                            name: '对话流程',
                            content: [
                                'Step 1. 自我介绍，确认身份。',
                                ' - Step 1.1 如果商家明确表示肯定（如 "是啊","对"），进入Step 2，介绍来意。',
                                ' - Step 1.2 如果商家没有明确回复（如 "喂"，"啊？"），或者只能听取到噪音，请再次询问商家。【推荐话术：喂，您听得到吗？我这边是美团的，请问是${poiName}的老板是吗？】',
                                ' - Step 1.3 如果商家没表示否定，则礼貌挂断电话。【推荐话术：好的，那先不打扰了，再见<hangup>】',
                                'Step 2. 介绍来意。【建议话术：XXXX】然后进入Step 3，切入主线任务。 ',
                                'Step 3. 切入主线任务。【建议话术：XXXX】',
                                'Step 4. 二次确认。【建议话术：好嘞，那我这边再确认下[二次确认信息]。大致就是这样，您看对不？】',
                                '- Step 4.1 如果商家表示赞同，则礼貌结束通话。【推荐话术：好的好的，那就先这样啦。感谢您的时间，再见<hangup>。】',
                                '- Step 4.2 如果商家表示异议，请根据他的回答，对[二次确认信息]进行更正，并询问他是否符合要求，直到他表示没有异议后，礼貌挂断。【推荐话术：好的好的，如果没有其他需要补充的，那就先这样啦。感谢您的时间，再见<hangup>。】',
                            ],
                            allowFrontLineChange: true,
                            requiredFrontLineChange: true,
                        },
                        知识点: {
                            name: '知识点',
                            content: ['请补充需模型学习的知识'],
                            allowFrontLineChange: true,
                            requiredFrontLineChange: true,
                        },
                        约束: {
                            name: '约束',
                            content: ['请补充必要的约束，如"不可骂人"。'],
                            allowFrontLineChange: true,
                            requiredFrontLineChange: true,
                        },
                    },
                    prompt: DEFAULT_HEAD_PROMPT,
                };
            }
            // form.resetFields();
            form.setFieldsValue(defaultValues);
        }
    }, [formInitialized, form, id, isFrontLine]);

    const fetchTemplateById = async id => {
        try {
            const res = await getTemplateById(id);
            if (res) {
                if (res.type) {
                    setTemplateType(res.type);
                }
                // 先使用processFormData处理数据
                const processedData = processFormData(res);

                // 将inputParams从数组转换为对象格式
                if (
                    processedData.systemPrompt &&
                    Array.isArray(processedData.systemPrompt.inputParams)
                ) {
                    const inputParamsObj = convertInputParamsToObject(
                        processedData.systemPrompt.inputParams,
                    );
                    // 使用类型断言解决类型问题
                    processedData.systemPrompt.inputParams =
                        inputParamsObj as any;
                }

                console.log('转换后的表单数据:', processedData);
                // 设置表单值
                setFormDataAndValidate(processedData);
                setLoading(false);
            }
        } catch (error) {
            console.error('获取模板详情失败:', error);
            Toast.fail('获取模板详情失败，请稍后重试');
            setLoading(false);
        }
    };

    useEffect(() => {
        // 加载表单数据
        setLoading(true);
        if (id) {
            // 根据ID获取模板详情
            fetchTemplateById(id);
        } else {
            // 新增模式 - 创建空对象初始化表单
            setLoading(false);
            return;
        }
    }, [id]);

    const handlePoiCall = () => {
        console.log(getOcrmHost());
        window.open(`${getOcrmHost()}/page/dove/communicationAgent`, '_blank');
    };

    // 添加取消按钮处理函数
    const handleCancel = () => {
        const env = getEnvironment();
        const isLocal = env === Environment.DEV;

        // 确定返回的URL
        const url = isLocal ? '/agentManagement' : '/page/dove/agentManagement';

        // 直接跳转回原页面
        window.location.href = url;
    };

    const handleConfirmPublish = async () => {
        try {
            setLoading(true);
            let templateId = id as any;

            // 如果是新建模式，需要先保存获取ID
            if (!isEdit || !id) {
                const saveResult = await handleSave();
                if (saveResult) {
                    // 对于新增模式，addTemplate返回的结果是在result.data中
                    templateId = saveResult;
                    if (!templateId) {
                        Toast.fail('保存失败，无法获取模板ID');
                        setLoading(false);
                        return;
                    }
                } else {
                    Toast.fail('保存失败，无法发布');
                    setLoading(false);
                    return;
                }
            } else {
                // 编辑模式，先保存当前修改
                const saveResult = await handleSave();
                if (!saveResult) {
                    Toast.fail('更新失败，无法发布');
                    setLoading(false);
                    return;
                }
                // 编辑模式下使用当前ID
            }

            // 调用发布API
            if (templateId) {
                const res = await publishAgent(String(templateId));
                if (res.code === 0) {
                    Toast.success('发布成功');
                } else {
                    Toast.fail('发布失败: ' + (res.message || '未知错误'));
                }
            } else {
                Toast.fail('缺少模板ID，无法发布');
            }
        } catch (error) {
            console.error('发布过程中出错:', error);
            Toast.fail('发布过程出错，请重试');
        } finally {
            setLoading(false);
        }
    };

    const handlePublish = () => {
        // 根据当前状态显示不同的确认信息
        const confirmTitle = isEdit ? '确认更新并发布?' : '确认保存并发布?';
        const confirmContent = isEdit
            ? '发布前将先保存您的最新修改，确定要继续吗?'
            : '您正在创建新的模板，发布前需要先保存，确定要继续吗?';

        Modal.confirm({
            title: confirmTitle,
            content: confirmContent,
            onOk: handleConfirmPublish,
            okText: '确认',
            cancelText: '取消',
        });
    };

    return (
        <Spin
            style={{
                position: 'absolute',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
            }}
            spinning={loading}
        >
            {/* 添加页面级加载效果 */}
            <FormPro
                form={form}
                grid24
                labelCol={{ span: 3 }}
                labelAlign="right"
                onValuesChange={(changedValues, allValues) => {
                    console.log('changedValues', changedValues);
                    console.log('allValues', allValues);
                }}
                wrapperCol={{ span: 21 }}
                colon={false}
            >
                <div className="bd-deciliter-container">
                    {!isFrontLine &&
                        formInitialized &&
                        templateType !== AgentTypeEnum.CITY && (
                            <HeadquartersForm form={form} id={id as string} />
                        )}
                    {(isFrontLine || templateType === AgentTypeEnum.CITY) &&
                        formInitialized && (
                            <FrontLineForm
                                form={form}
                                onSaveOriginalData={setOriginalData}
                            />
                        )}

                    <div className="bd-deciliter-header-dialog-flow">
                        {formInitialized && <DialogFlow form={form} />}
                    </div>
                </div>
                <div className="bd-deciliter-footer">
                    <Button type="hollow" onClick={handleCancel}>
                        取消
                    </Button>
                    <Button type="brand" onClick={handlePoiCall}>
                        发起商家外呼
                    </Button>
                    <Button
                        type="brand"
                        onClick={handleSave}
                        disabled={loading}
                    >
                        {isEdit ? '更新' : '保存'}
                    </Button>
                    <Button
                        type="brand"
                        onClick={handlePublish}
                        disabled={loading}
                    >
                        发布
                    </Button>
                </div>
            </FormPro>
        </Spin>
    );
};

const App = () => {
    return (
        <BDDeciliterProvider>
            <AppContent />
        </BDDeciliterProvider>
    );
};

export default App;
