import React, { useState, useEffect, useContext } from 'react';
import { FormPro, Input } from '@roo/roo';
import { Button, Select, Checkbox } from 'antd';
import { BDDeciliterContext } from '../../context';
import './styles/IntentionLabelConfig.scss';
import './styles/ParamCard.scss';
// 导入图片
import editIcon from '../../assets/image/edit-icon.svg';
import deleteIcon from '../../assets/image/del-icon.svg';
import addIcon from '../../assets/image/add-icon.svg';

const IntentionLabelContainer = ({
    form,
    name,
    disabled = false,
    formFilters = ['tag', 'description', 'tagPrompt'], // 自定义表单项过滤
    allowBDChangeConfig = false, // 是否允许BD修改配置
    allBtnText = '添加标签', // 添加按钮文本
    canEditByBD = false,
}) => {
    const { isFrontLine } = useContext(BDDeciliterContext);
    const [editingIds, setEditingIds] = useState<number[]>([]);
    const [originalValues, setOriginalValues] = useState<Record<number, any>>(
        {},
    );

    // 检查字段是否为空
    const checkFieldEmpty = fieldName => {
        return formFilters.some(item => {
            const value = form.getFieldValue([...name, fieldName, item]);
            return !value;
        });
    };

    // 判断是否是真正的初始状态（无数据或所有字段均为空）
    const isInitialState = () => {
        const values = form.getFieldValue(name) || [];
        if (values.length === 0) return true;

        // 检查是否所有字段都为空
        const allEmpty = values.every((_, index) => checkFieldEmpty(index));
        return allEmpty;
    };

    // 判断是否有有效数据（非空）
    const hasValidData = () => {
        const values = form.getFieldValue(name) || [];
        if (values.length === 0) return false;

        // 检查是否有任何一项数据的任意字段非空
        return values.some(item => formFilters.some(key => !!item?.[key]));
    };

    const getNewField = () => {
        // 添加空的原始值记录
        const emptyObj = {};
        formFilters.forEach(item => (emptyObj[item] = ''));
        return emptyObj;
    };

    // 仅在真正的初始状态（无数据或所有数据为空）时，才自动进入编辑模式
    useEffect(() => {
        // 如果禁用状态，不自动进入编辑模式
        if (disabled) {
            setEditingIds([]);
            return;
        }

        const values = form.getFieldValue(name) || [];

        // 如果有有效数据，则不设置编辑状态
        if (hasValidData()) {
            setEditingIds([]);
        } else if (isInitialState() && values.length > 0) {
            // 只有在真正的初始状态且有字段时，才将第一项设为编辑状态
            setEditingIds([0]);

            // 保存初始空值
            setOriginalValues(prev => ({
                ...prev,
                0: getNewField(),
            }));
        }
    }, [form, name, disabled]);

    const handleSave = async fieldName => {
        try {
            // 1. 生成所有需要校验的字段名
            const fields = formFilters.map(item => [...name, fieldName, item]);

            // 2. 校验所有必填字段
            await form.validateFields(fields);

            // 3. 获取所有字段的值
            const values = {};
            let hasEmpty = false;
            formFilters.forEach(item => {
                const value = form.getFieldValue([...name, fieldName, item]);
                values[item] = value;
                if (!value) hasEmpty = true;
            });

            // 4. 检查所有字段是否都有值
            if (hasEmpty) {
                console.error('请完成意向配置！');
                return;
            }

            // 5. 确保值被正确设置到表单中
            const fieldsToSet = formFilters.map(item => ({
                name: [...name, fieldName, item],
                value: values[item],
            }));
            form.setFields(fieldsToSet);

            // 6. 验证通过后，从编辑状态列表中移除该字段
            setEditingIds(prev => prev.filter(id => id !== fieldName));
        } catch (error) {
            console.error('验证失败:', error);
        }
    };

    const startEditing = fieldName => {
        // 如果禁用状态，不允许编辑
        if (disabled) return;

        const original = {};
        formFilters.forEach(item => {
            original[item] = form.getFieldValue([...name, fieldName, item]);
        });
        setOriginalValues(prev => ({
            ...prev,
            [fieldName]: original,
        }));

        setEditingIds(prev => [...prev, fieldName]);
    };

    const handleCancel = (fieldName, isOnlyOne, remove) => {
        const isEmpty = checkFieldEmpty(fieldName);

        if (isEmpty && !isOnlyOne) {
            // 如果是空项且不是唯一一项，则直接删除
            remove(fieldName);
        } else {
            // 恢复原始值
            const original = originalValues[fieldName];
            if (original) {
                const fieldsToRestore = formFilters.map(item => ({
                    name: [...name, fieldName, item],
                    value: original[item],
                }));
                form.setFields(fieldsToRestore);
            }

            // 退出编辑模式
            setEditingIds(prev => prev.filter(id => id !== fieldName));
        }
    };

    const handleFormItems = ({ defaultTagName, disabled }) => {
        const formItems = [
            {
                label: '标签名称',
                name: 'tag',
                rules: [
                    {
                        required: true,
                        message: '请输入标签名称',
                    },
                ],
                initialValue: defaultTagName,
                disabled: true,
            },
            {
                label: '标签概述',
                name: 'description',
                rules: [
                    {
                        required: true,
                        message: '请输入标签概述',
                    },
                ],
                disabled: disabled,
            },
            {
                label: '中文名称',
                name: 'showName',
                rules: [
                    {
                        required: true,
                        message: '请输入中文名称',
                    },
                ],
                initialValue: '示例：商家微信号码',
                disabled: disabled,
            },
            {
                label: '参数名',
                name: 'name',
                rules: [
                    {
                        required: true,
                        message: '请输入参数名',
                    },
                ],
                disabled: disabled,
            },
            {
                label: '数据类型',
                name: 'dataType',
                rules: [
                    {
                        required: true,
                        message: '请选择数据类型',
                    },
                ],
                disabled: disabled,
                initialValue: 1,
                children: (
                    <Select
                        options={[
                            {
                                label: '文本',
                                value: 1,
                            },
                            {
                                label: '数字',
                                value: 2,
                            },
                            {
                                label: '小数',
                                value: 3,
                            },
                        ]}
                        style={{ width: '100%' }}
                    />
                ),
            },
            {
                label: '专属prompt',
                name: 'tagPrompt',
                rules: [
                    {
                        required: true,
                        message: '请输入专属prompt',
                    },
                ],
                disabled: disabled,
            },
        ].filter(item => formFilters.includes(item.name));
        return formItems;
    };

    // 渲染编辑态
    const renderEditItem = (field, formItems, isOnlyOne, remove) => (
        <div key={field.key} className="intention-label-editing">
            {formItems.map(item => (
                <FormPro.Item
                    key={item.name}
                    label={item.label}
                    name={[field.name, item.name]}
                    labelCol={{ span: 4 }}
                    grid24
                    labelAlign="right"
                    wrapperCol={{ span: 20 }}
                    rules={item.rules}
                    initialValue={item.initialValue}
                >
                    {item?.children ? (
                        item.children
                    ) : (
                        <Input disabled={item.disabled} />
                    )}
                </FormPro.Item>
            ))}
            <div className="intention-label-actions">
                <Button
                    onClick={() => handleCancel(field.name, isOnlyOne, remove)}
                    disabled={disabled}
                >
                    取消
                </Button>
                <Button
                    type="primary"
                    onClick={() => handleSave(field.name)}
                    disabled={disabled}
                >
                    保存
                </Button>
            </div>
        </div>
    );

    // 渲染查看态
    const renderViewItem = (field, isOnlyOne, remove) => {
        const defaultFields = handleFormItems({
            defaultTagName: String.fromCharCode(65 + Number(field.key)),
            disabled,
        });

        // 判断是否可以编辑
        const canEdit =
            !isFrontLine ||
            (allowBDChangeConfig &&
                form.getFieldValue([
                    ...name,
                    field.name,
                    'allowFrontLineChange',
                ]));

        return (
            <div key={field.key} className="param-card">
                <div className="param-card-header">
                    {form.getFieldValue([...name, field.name, 'tag']) ? (
                        <div className="param-card-header-title">
                            {form.getFieldValue([...name, field.name, 'tag']) ||
                                '暂无内容'}
                        </div>
                    ) : null}
                    <div className="param-card-header-actions">
                        {canEditByBD && (
                            <FormPro.Item
                                name={[field.name, 'allowFrontLineChange']}
                                valuePropName="checked"
                                label=" "
                                colon={false}
                                initialValue={allowBDChangeConfig}
                                style={{ height: '32px', marginBottom: '0px' }}
                            >
                                <Checkbox>
                                    <span style={{ whiteSpace: 'nowrap' }}>
                                        一线可修改
                                    </span>
                                </Checkbox>
                            </FormPro.Item>
                        )}
                        {canEdit ? (
                            <img
                                src={editIcon}
                                alt="编辑"
                                className="param-card-icon"
                                onClick={() => startEditing(field.name)}
                                style={
                                    disabled
                                        ? {
                                              cursor: 'not-allowed',
                                              opacity: 0.5,
                                          }
                                        : {}
                                }
                            />
                        ) : null}
                        {!isOnlyOne && (
                            <img
                                src={deleteIcon}
                                alt="删除"
                                className="param-card-icon"
                                onClick={() => !disabled && remove(field.name)}
                                style={
                                    disabled
                                        ? {
                                              cursor: 'not-allowed',
                                              opacity: 0.5,
                                          }
                                        : {}
                                }
                            />
                        )}
                    </div>
                </div>
                <div className="param-card-content">
                    {defaultFields.map(item => {
                        // 特殊字段特殊处理
                        if (item.name === 'tag') {
                            return null;
                        }
                        // 其它字段通用处理
                        return (
                            <div className="param-card-item" key={item.name}>
                                <div className="param-card-label">
                                    {item.label}
                                </div>
                                <div className="param-card-value">
                                    {form.getFieldValue([
                                        ...name,
                                        field.name,
                                        item.name,
                                    ]) || '暂无内容'}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    return (
        <>
            <FormPro.List name={name} initialValue={[{}]}>
                {(fields, { add, remove }) => (
                    <div className="intention-label-list">
                        {fields.map(field => {
                            const isEditing = editingIds.includes(field.name);
                            const isOnlyOne = fields.length <= 1;
                            const defaultTagName = String.fromCharCode(
                                65 + Number(field.key),
                            );
                            const formItems = handleFormItems({
                                defaultTagName,
                                disabled,
                            });

                            return isEditing
                                ? renderEditItem(
                                      field,
                                      formItems,
                                      isOnlyOne,
                                      remove,
                                  )
                                : renderViewItem(field, isOnlyOne, remove);
                        })}

                        <Button
                            type="text"
                            onClick={() => {
                                if (disabled) return;
                                const newFieldIndex = add(); //不要删掉，控制新增
                                // 添加空的原始值记录
                                const emptyObj = getNewField();
                                setOriginalValues(prev => ({
                                    ...prev,
                                    [fields.length]: emptyObj,
                                }));
                                // 新增的标签默认为编辑状态
                                setEditingIds(prev => [...prev, fields.length]);
                            }}
                            block
                            className="intention-label-add-btn"
                            disabled={disabled}
                        >
                            <div className="intention-label-add-btn-container">
                                <img src={addIcon}></img> {allBtnText}
                            </div>
                        </Button>
                    </div>
                )}
            </FormPro.List>
        </>
    );
};

export default IntentionLabelContainer;
