.intention-label-config {

    // padding-top: 24px;
    .rich-editor-wrapper {
        .ql-editor {
            .mention {
                background: #edf6ff;
                font-weight: 400;
                color: #198cff;
                font-family: PingFang SC;
                font-size: 12px;
                line-height: 16px;
                text-align: left;
            }
        }
    }
}

.intention-label-add-btn {
    background-color: #f5f6fa;
    border-radius: 2px;
    padding: 10px;

    .intention-label-add-btn-container {
        display: flex;
        align-items: center;

        img {
            margin-right: 4px;
        }
    }
}

.intention-label-container {
    margin-top: 10px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 16px;
    background-color: #f5f6fa;

    .roo-form-pro-item-label {
        text-align: left;
    }

    .intention-label-header {
        margin-bottom: 16px;
    }

    .intention-label-item {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-bottom: 16px;

        .roo-form-pro-item {
            flex: 1;
        }

        .delete-icon {
            position: absolute;
            right: 8px;
            top: 0;
            color: #ff4d4f;
            font-size: 16px;
            cursor: pointer;
        }
    }

    .dynamic-form-item {
        margin-bottom: 16px;

        .dynamic-delete-button {
            cursor: pointer;
            color: #999;
            font-size: 16px;
            transition: all 0.3s;

            &:hover {
                color: #ff4d4f;
            }
        }

        .edit-icon {
            cursor: pointer;
            color: #999;
            font-size: 16px;
            transition: all 0.3s;

            &:hover {
                color: #1890ff;
            }
        }
    }

    .label-preview-item {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        .label-tag {
            color: #1890ff;
        }

        .label-preview-content {
            .label-section {
                margin-bottom: 12px;

                .label-title {
                    font-weight: 500;
                    margin-bottom: 4px;
                    color: #666;
                }

                .label-value {
                    color: #333;
                    line-height: 1.5;
                    white-space: pre-wrap;
                }
            }
        }
    }

    .intention-label-card {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;

        .roo-form-pro-item {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
                text-align: right;
            }
        }
    }

    // 添加标签按钮样式
    .roo-btn-dashed {
        border-color: #d9d9d9;
        background: #fff;

        &:hover,
        &:focus {
            color: #1890ff;
            border-color: #1890ff;
        }
    }
}

// 添加新的样式类
.intention-label-list {
    display: flex;
    row-gap: 16px;
    flex-direction: column;
}

.intention-label-editing {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f5f6fa;

    .doveCustomRoo-form-pro-item:first-child {
        margin-top: 12px;
    }

    .intention-label-actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        gap: 8px; // 按钮之间的间距
    }

    .doveCustomRoo-form-pro-item-label {
        min-width: 100px;
    }
}

.intention-label-add-btn {
    // 添加按钮样式
}