.param-card {
    background: rgba(245, 246, 250, 0.6);
    padding: 12px;
    border-radius: 2px;
    position: relative;
    width: 100%;
    flex: 1;

    .dynamic-form-list {
        background: none;
    }

    .param-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        margin-bottom: 4px;

        .param-card-header-title {
            color: #198cff;
            background-color: #edf6ff;
            padding: 2px 4px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            text-align: left;
            display: flex;
            border-radius: 2px;
            align-items: center;
        }

        .param-card-header-check {
            display: flex;
            align-items: center;
            gap: 12px;

            .doveCustomRoo-form-pro-control-input-content {
                display: flex;
                align-items: center;
            }

            :global {
                .roo-form-pro-item {
                    margin-bottom: 0;
                    display: flex;
                    align-items: center;
                }

                .roo-row {
                    display: flex;
                    align-items: center;
                }

                .roo-form-pro-item-control {
                    line-height: 1;
                    display: flex;
                    align-items: center;
                }

                .roo-checkbox-wrapper {
                    display: flex;
                    align-items: center;
                }
            }
        }

        .param-card-header-actions {
            display: flex;
            align-items: center;
            position: absolute;
            right: 8px;
            top: 12px;

            .param-card-checkbox {
                margin-right: 8px;
            }

            .param-card-icon {
                cursor: pointer;
                margin-left: 8px;
            }
        }
    }

    .param-card-content {
        padding: 8px 0;

        .param-card-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;

            &:last-child {
                margin-bottom: 0;
            }

            .param-card-label {
                margin-right: 8px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                min-width: 100px;
            }

            .param-card-value {
                color: rgba(0, 0, 0, 0.85);
                font-size: 14px;
                line-height: 1.5;
                white-space: pre-wrap;
            }
        }
    }
}