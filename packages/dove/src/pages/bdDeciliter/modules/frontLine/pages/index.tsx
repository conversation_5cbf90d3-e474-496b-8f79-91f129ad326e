import React, { useState, useEffect, useContext } from 'react';
import { FormInstance, Select, Input as AntInput, Form, App } from 'antd';
import ScrollableSelect from '../../../components/ScrollableSelect';
import type { FormInstance as RooFormInstance } from '@roo/roo/FormPro/hooks/useForm';
import { FormPro, Input, Selector, Tabs } from '@roo/roo';
import VoiceSelector from '@src/pages/bdDeciliter/common/components/VoiceSelector';
import KeywordExtract from '../../headquarters/pages/KeywordExtract';
import {
    getKnowledgeBaseList,
    getSkillList,
    getUserName,
} from '../../../services';
import PromptConfig from '@src/pages/bdDeciliter/common/components/PromptConfig';
import DynamicFormList from '@src/pages/bdDeciliter/common/components/DynamicFormList';
import IntentionLabelContainer from '@src/pages/bdDeciliter/common/components/IntentionLabelContainer';
import {
    convertInputParamsToObject,
    processFormData,
    processInputParams,
} from '@src/pages/bdDeciliter/common/utils';
import { BDDeciliterContext } from '../../../context';
import './styles/index.scss';

interface FrontLineFormProps {
    form: FormInstance | RooFormInstance<any> | any;
    onSaveOriginalData: (data: any) => void;
}

const { Pane: TabPane } = Tabs;

const FrontLineForm: React.FC<FrontLineFormProps> = ({
    form,
    onSaveOriginalData,
}) => {
    const { isFrontLine } = useContext(BDDeciliterContext);
    const [value, setValue] = useState<string>('tab1');
    const [userName, setUserName] = useState('');
    const [loading, setLoading] = useState<boolean>(false);
    const [voiceOptions, setVoiceOptions] = useState<any[]>([]);
    const [skillList, setSkillList] = useState<any[]>([]);
    const [knowledgeBaseList, setKnowledgeBaseList] = useState<any[]>([]);
    const [llmOptions, setLlmOptions] = useState<any[]>([]);
    const { modal } = App.useApp();
    const [modalForm] = Form.useForm();

    const templateType = Form.useWatch('type', form);

    useEffect(() => {
        // 并行获取所有下拉选项数据
        const fetchData = async () => {
            try {
                const [skillRes, knowledgeRes] = await Promise.all([
                    getSkillList({ page: 1, pageSize: 100 }),
                    getKnowledgeBaseList(),
                ]);

                // 处理技能列表数据
                const skillOptions = skillRes.list.map(item => ({
                    label: item.skillConfig?.showName || '未命名技能',
                    value: item.id,
                }));
                setSkillList(skillOptions);

                // 处理知识库列表数据
                const knowledgeBaseOptions = knowledgeRes.list.map(item => ({
                    label: item.name || '未命名知识库',
                    value: item.id,
                }));
                setKnowledgeBaseList(knowledgeBaseOptions);

                // // 设置其他选项数据
            } catch (error) {
                console.error('获取选项数据失败:', error);
            }
        };

        fetchData();
    }, []);

    // 处理场景模板变化
    const handleSceneChange = async (value: string, option: any) => {
        // 确保option是对象而不是数组
        const selectedOption = Array.isArray(option) ? option[0] : option;
        const {
            id,
            value: sceneName,
            label,
            isDefaultSelected,
            ...sceneData
        } = selectedOption;
        // 检查sceneData是否为有效对象
        if (!sceneData || typeof sceneData !== 'object') {
            console.error('无效的场景模板数据:', selectedOption);
            return;
        }

        const originalFormData = processFormData(sceneData);

        // 将inputParams从数组转换为对象格式
        if (
            originalFormData?.systemPrompt &&
            Array.isArray(originalFormData.systemPrompt.inputParams)
        ) {
            const inputParamsObj = convertInputParamsToObject(
                originalFormData.systemPrompt.inputParams,
            );
            // 使用类型断言解决类型问题
            originalFormData.systemPrompt.inputParams = inputParamsObj as any;
        }

        // 0612新增，添加parentAgentId字段；
        if (id) {
            originalFormData.parentAgentId = id;
        }

        onSaveOriginalData({ ...originalFormData, type: 3 });
        const formattedData = processInputParams(sceneData);

        if (userName) {
            formattedData.name = userName
                ? `${formattedData.name}-${userName}`
                : formattedData.name;
        } else {
            const res = await getUserName();

            if (res?.code === 0) {
                const { misId } = (res as any).data;
                setUserName(misId);
                formattedData.name = misId
                    ? `${formattedData.name}-${misId}`
                    : formattedData.name;
            }
        }

        form.setFieldsValue(formattedData);

        // 确保表单中的特殊字段被正确设置，即使为空
        const specialFields = ['对话流程', '知识点', '约束'];
        specialFields.forEach(field => {
            const fieldPath = ['systemPrompt', 'inputParams', field];
            const currentValue = form.getFieldValue(fieldPath);
            if (!currentValue || !currentValue.content) {
                form.setFields([
                    {
                        name: fieldPath,
                        value: {
                            content: [''],
                            allowFrontLineChange: true,
                            requiredFrontLineChange: true,
                        },
                    },
                ]);
            }
        });
    };

    // 初始化表单时确保allowFrontLineChange字段存在
    useEffect(() => {
        const intentionAllowFrontLineChange = form.getFieldValue([
            'intention',
            'allowFrontLineChange',
        ]);
        if (intentionAllowFrontLineChange === undefined) {
            form.setFields([
                {
                    name: ['intention', 'allowFrontLineChange'],
                    value: true, // 默认允许前线修改
                },
            ]);
        }
    }, [form]);

    // 判断意向标签配置是否可编辑
    const isIntentionLabelEditable = () => {
        return (
            isFrontLine &&
            form.getFieldValue(['intention', 'allowFrontLineChange'])
        );
    };

    const isKnowledgeBaseEditable = () => {
        return (
            isFrontLine &&
            form.getFieldValue(['knowledgeBase', 'allowFrontLineChange'])
        );
    };

    const isSkillEditable = () => {
        return (
            isFrontLine && form.getFieldValue(['skill', 'allowFrontLineChange'])
        );
    };

    const handleOpenModelParamsConfig = () => {
        // 回显主表单的参数
        const maxRecall = form.getFieldValue(['modelConfig', 'maxRecall']);
        const simThreshold = form.getFieldValue([
            'modelConfig',
            'simThreshold',
        ]);
        modalForm.setFieldsValue({ maxRecall, simThreshold });

        modal.confirm({
            title: '输入输出配置',
            icon: null,
            width: 600,
            content: (
                <Form
                    form={modalForm}
                    layout="vertical"
                    style={{ marginTop: 24 }}
                >
                    <Form.Item
                        label="FAQ最大召回数量"
                        name="maxRecall"
                        rules={[
                            {
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                message: '请输入大于等于0的数字',
                            },
                        ]}
                        initialValue={'5'}
                    >
                        <AntInput placeholder="请输入" />
                    </Form.Item>
                    <Form.Item
                        label="FAQ相似度阈值"
                        name="simThreshold"
                        rules={[
                            {
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                message: '请输入大于等于0的数字',
                            },
                        ]}
                    >
                        <AntInput placeholder="请输入" />
                    </Form.Item>
                </Form>
            ),
            okText: '保存',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const values = await modalForm.validateFields();
                    form.setFieldValue(
                        ['modelConfig', 'maxRecall'],
                        values.maxRecall,
                    );
                    form.setFieldValue(
                        ['modelConfig', 'simThreshold'],
                        values.simThreshold,
                    );
                } catch (e) {
                    return Promise.reject();
                }
            },
        });
    };

    return (
        <div className="bd-deciliter-header-tabs-container">
            <Tabs
                value={value}
                onChange={activeKey => setValue(activeKey as string)}
                type="fill"
            >
                <TabPane label="基础配置" name="tab1" forceRender>
                    <div className="basic-config">
                        <FormPro.Item
                            name={['modelConfig', 'maxRecall']}
                            noStyle
                        />
                        <FormPro.Item
                            name={['modelConfig', 'simThreshold']}
                            noStyle
                        />
                        <FormPro.Item
                            label="模板类型"
                            name="type"
                            noStyle
                        ></FormPro.Item>
                        <FormPro.Item
                            label="场景模板"
                            name="sceneName"
                            rules={[
                                { required: true, message: '请选择场景模板' },
                            ]}
                        >
                            <ScrollableSelect
                                placeholder="请选择场景模板"
                                apiPath="/xianfu/api-v2/dove/agent/query"
                                apiMethod="post"
                                labelField="sceneName"
                                valueField="sceneName"
                                loading={loading}
                                onChange={handleSceneChange}
                                needConfirm={true}
                                confirmTitle="切换场景模板确认"
                                confirmContent="切换场景模板后，当前未保存的内容可能会丢失。"
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="模板名称"
                            name="name"
                            rules={[
                                { required: true, message: '请输入模板名称' },
                            ]}
                        >
                            <Input
                                placeholder="请输入模板名称"
                                style={{ width: '100%' }}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="木星租户ID"
                            name={['agentChannel', 0, 'jupiterTenantId']}
                            noStyle
                        />
                        <FormPro.Item
                            label="音色"
                            name={['agentChannel', 0, 'timbreId']}
                            rules={[{ required: true, message: '请选择音色' }]}
                        >
                            <VoiceSelector
                                options={voiceOptions}
                                style={{ width: '100%' }}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="身份定义"
                            name={[
                                'systemPrompt',
                                'inputParams',
                                '身份信息',
                                'content',
                            ]}
                            // wrapperCol={{ span: 10 }}
                            rules={[
                                {
                                    required: form.getFieldValue([
                                        'systemPrompt',
                                        'inputParams',
                                        '身份信息',
                                        'requiredFrontLineChange',
                                    ]),
                                    message: '请输入身份定义',
                                },
                            ]}
                        >
                            <Input.Textarea
                                placeholder="请输入身份定义"
                                style={{ width: '100%' }}
                                disabled={
                                    !form.getFieldValue([
                                        'systemPrompt',
                                        'inputParams',
                                        '身份信息',
                                        'allowFrontLineChange',
                                    ])
                                }
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            name={[
                                'systemPrompt',
                                'inputParams',
                                '身份信息',
                                'allowFrontLineChange',
                            ]}
                            noStyle
                        />
                        <FormPro.Item
                            label="开场白"
                            name={'prologue'}
                            // wrapperCol={{ span: 10 }}
                            rules={[
                                { required: true, message: '请输入开场白' },
                            ]}
                            shouldUpdate={(
                                prevValues: any,
                                currentValues: any,
                            ) => {
                                return (
                                    JSON.stringify(prevValues?.prologue) !==
                                    JSON.stringify(currentValues?.prologue)
                                );
                            }}
                        >
                            <PromptConfig
                                form={form}
                                isShowTitle={false}
                                className="prologue-editor"
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            name={['systemPrompt', 'prompt']}
                            noStyle
                        ></FormPro.Item>
                        <FormPro.Item
                            label="对话流程"
                            // wrapperCol={{ span: 10 }}
                            name={['systemPrompt', 'inputParams', '对话流程']}
                            rules={[
                                { required: true, message: '请输入对话流程' },
                            ]}
                        >
                            <DynamicFormList
                                name={[
                                    'systemPrompt',
                                    'inputParams',
                                    '对话流程',
                                ]}
                                form={form}
                                isFrontLine={isFrontLine}
                                required={form.getFieldValue([
                                    'systemPrompt',
                                    'inputParams',
                                    '对话流程',
                                    'requiredFrontLineChange',
                                ])}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="知识点"
                            name={['systemPrompt', 'inputParams', '知识点']}
                            required={form.getFieldValue([
                                'systemPrompt',
                                'inputParams',
                                '知识点',
                                'requiredFrontLineChange',
                            ])}
                        >
                            <DynamicFormList
                                name={['systemPrompt', 'inputParams', '知识点']}
                                form={form}
                                isFrontLine={isFrontLine}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="约束"
                            name={['systemPrompt', 'inputParams', '约束']}
                            required={form.getFieldValue([
                                'systemPrompt',
                                'inputParams',
                                '约束',
                                'requiredFrontLineChange',
                            ])}
                        >
                            <DynamicFormList
                                name={['systemPrompt', 'inputParams', '约束']}
                                form={form}
                                isFrontLine={isFrontLine}
                            />
                        </FormPro.Item>
                        <FormPro.Item name={['skill', 'ids']} label="关联技能">
                            <Select
                                allowClear
                                mode="multiple"
                                style={{
                                    flex: 1,
                                    marginRight: '8px',
                                    width: '100%',
                                }}
                                placeholder="请选择关联技能"
                                options={skillList}
                                disabled={!isSkillEditable()}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            name={['knowledgeBase', 'ids']}
                            label="关联知识库"
                        >
                            <Select
                                mode="multiple"
                                style={{
                                    flex: 1,
                                    marginRight: '8px',
                                    width: '100%',
                                }}
                                placeholder="请选择关联知识库"
                                options={knowledgeBaseList}
                                disabled={!isKnowledgeBaseEditable()}
                            />
                        </FormPro.Item>
                    </div>
                </TabPane>
                <TabPane label="意向标签配置" name="tab2" forceRender>
                    <div className={'intention-label-config basic-config'}>
                        <FormPro.Item
                            label="大模型"
                            name={['intention', 'llmName']}
                            rules={[{ required: true, message: '请选择模型' }]}
                        >
                            <Selector
                                fieldNames={{ label: 'name', value: 'id' }}
                                placeholder="请选择模型"
                                options={llmOptions}
                                style={{ width: '100%' }}
                                disabled={!isIntentionLabelEditable()}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            label="Prompt"
                            name={['intention', 'systemPrompt']}
                        >
                            <AntInput.TextArea
                                style={{ minHeight: 300 }}
                                placeholder="请输入prompt"
                                disabled={!isIntentionLabelEditable()}
                            ></AntInput.TextArea>
                        </FormPro.Item>
                        <FormPro.Item
                            label="意向标签配置"
                            rules={[
                                { required: true, message: '请完成意向配置！' },
                            ]}
                        >
                            <IntentionLabelContainer
                                name={['intention', 'elementList']}
                                form={form}
                                disabled={!isIntentionLabelEditable()}
                                canEditByBD={isIntentionLabelEditable()}
                            />
                        </FormPro.Item>
                        <FormPro.Item
                            name={['intention', 'allowFrontLineChange']}
                            noStyle
                        />
                    </div>
                </TabPane>
                <TabPane label="关键信息提取" name="tab3" forceRender>
                    <KeywordExtract form={form} llmOptions={llmOptions} />
                </TabPane>
            </Tabs>
        </div>
    );
};

export default FrontLineForm;
