import React, { useEffect } from 'react';
import { FormPro, Input, Selector } from '@roo/roo';
import { Checkbox, Input as AntInput } from 'antd';
import { useUpdateEffect } from 'ahooks';
import IntentionLabelContainer from '../../../../common/components/IntentionLabelContainer';

interface IntentionConfigProps {
    form: any;
    llmOptions: any[];
    showIntentionLlmAppId: boolean;
    submittedTenantId: string;
    isLoadingLlm: boolean;
}

const IntentionConfig: React.FC<IntentionConfigProps> = ({
    form,
    llmOptions,
    showIntentionLlmAppId,
    submittedTenantId,
    isLoadingLlm,
}) => {
    // 监听意向标签的allowFrontLineChange变化
    const intentionAllowFrontLineChange = FormPro.useWatch(
        ['intention', 'allowFrontLineChange'],
        form,
    );

    // 处理意向标签allowFrontLineChange变化
    useUpdateEffect(() => {
        const elementList =
            form.getFieldValue(['intention', 'elementList']) || [];
        if (elementList.length > 0) {
            const updatedElementList = elementList.map((item: any) => ({
                ...item,
                allowFrontLineChange: intentionAllowFrontLineChange,
            }));
            form.setFieldValue(
                ['intention', 'elementList'],
                updatedElementList,
            );
            console.log(
                `意向标签allowFrontLineChange设为${intentionAllowFrontLineChange}，已同步更新所有elementList项`,
            );
        }
    }, [intentionAllowFrontLineChange]);

    return (
        <div className="intention-label-config basic-config">
            <FormPro.Item
                name={['intention', 'allowFrontLineChange']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
            >
                <Checkbox>
                    <span>一线可修改</span>
                </Checkbox>
            </FormPro.Item>
            <FormPro.Item
                label="大模型"
                name={['intention', 'llmName']}
                rules={[{ required: true, message: '请选择模型' }]}
                extra={
                    !submittedTenantId ? '请先填写并离开木星租户ID输入框' : null
                }
            >
                <Selector
                    fieldNames={{ label: 'name', value: 'id' }}
                    placeholder={
                        !submittedTenantId
                            ? '请先填写并离开木星租户ID输入框'
                            : '请选择模型'
                    }
                    options={llmOptions}
                    style={{ width: '100%' }}
                    disabled={!submittedTenantId || isLoadingLlm}
                />
            </FormPro.Item>
            {showIntentionLlmAppId && (
                <FormPro.Item
                    label="计费APPID"
                    name={['intention', 'llmAppId']}
                    rules={[
                        {
                            required: showIntentionLlmAppId,
                            message: '请输入计费APPID',
                        },
                    ]}
                >
                    <Input placeholder="请输入计费APPID"></Input>
                </FormPro.Item>
            )}
            <FormPro.Item
                label="Prompt"
                name={['intention', 'systemPrompt']}
                rules={[{ required: true, message: '请输入prompt' }]}
            >
                <AntInput.TextArea style={{ minHeight: 300 }} />
            </FormPro.Item>
            <FormPro.Item
                label="意向标签配置"
                rules={[
                    {
                        required: true,
                        message: '请选择意向标签配置',
                    },
                ]}
            >
                <IntentionLabelContainer
                    name={['intention', 'elementList']}
                    form={form}
                    allowBDChangeConfig={intentionAllowFrontLineChange}
                    canEditByBD={intentionAllowFrontLineChange}
                />
            </FormPro.Item>
        </div>
    );
};

export default IntentionConfig;
