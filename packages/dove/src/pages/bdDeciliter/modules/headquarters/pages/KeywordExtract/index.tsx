import { useContext } from 'react';
import { FormPro, Input, Selector } from '@roo/roo';
import { Checkbox, Input as AntInput } from 'antd';
import { useUpdateEffect } from 'ahooks';
import { BDDeciliterContext } from '@src/pages/bdDeciliter/context';
import IntentionLabelContainer from '../../../../common/components/IntentionLabelContainer';
import './index.scss';

export default function KeywordExtract(params: any) {
    const {
        form,
        llmOptions,
        showIntentionLlmAppId = true,
        showFrontLineEdit = false, // 是否展示一线可修改配置
    } = params;
    const { isFrontLine } = useContext(BDDeciliterContext);

    // 监听关键信息提取的enable的值
    const enableKeywordExtract = FormPro.useWatch(
        ['keywordExtractConfig', 'enable'],
        form,
    );

    // 监听关键信息提取的allowFrontLineChange变化
    const keywordAllowFrontLineChange = FormPro.useWatch(
        ['keywordExtractConfig', 'allowFrontLineChange'],
        form,
    );

    // 处理关键信息提取allowFrontLineChange变化
    useUpdateEffect(() => {
        const elementList =
            form.getFieldValue(['keywordExtractConfig', 'paramConfig']) || [];
        if (elementList.length > 0) {
            const updatedElementList = elementList.map((item: any) => ({
                ...item,
                allowFrontLineChange: keywordAllowFrontLineChange,
            }));

            form.setFieldValue(
                ['keywordExtractConfig', 'paramConfig'],
                updatedElementList,
            );
        }
    }, [keywordAllowFrontLineChange]);

    return (
        <div className="intention-label-config basic-config keyword-extract-container">
            {showFrontLineEdit ? (
                <>
                    <FormPro.Item
                        name={['keywordExtractConfig', 'enable']}
                        valuePropName="checked"
                        label=" "
                        colon={false}
                        initialValue={false}
                        className="keyword-extract-checkbox"
                    >
                        <Checkbox>
                            <span>启用该功能</span>
                        </Checkbox>
                    </FormPro.Item>
                    <FormPro.Item
                        name={['keywordExtractConfig', 'allowFrontLineChange']}
                        valuePropName="checked"
                        label=" "
                        colon={false}
                        initialValue={false}
                    >
                        <Checkbox disabled={!enableKeywordExtract}>
                            <span>一线可修改</span>
                        </Checkbox>
                    </FormPro.Item>
                </>
            ) : null}
            <div className="form-items-container">
                {!enableKeywordExtract && (
                    <div className="form-overlay">
                        <div className="overlay-text">请先启用该功能</div>
                    </div>
                )}
                <FormPro.Item
                    label="大模型"
                    name={['keywordExtractConfig', 'llmName']}
                    rules={[
                        {
                            required: enableKeywordExtract,
                            message: '请选择模型',
                        },
                    ]}
                >
                    <Selector
                        fieldNames={{ label: 'name', value: 'id' }}
                        placeholder="请选择模型"
                        options={llmOptions}
                        style={{ width: '100%' }}
                    />
                </FormPro.Item>
                {showIntentionLlmAppId && (
                    <FormPro.Item
                        label="计费APPID"
                        name={['keywordExtractConfig', 'llmAppId']}
                        rules={[
                            {
                                required: enableKeywordExtract,
                                message: '请输入计费APPID',
                            },
                        ]}
                    >
                        <Input placeholder="请输入计费APPID"></Input>
                    </FormPro.Item>
                )}
                <FormPro.Item
                    label="Prompt"
                    name={['keywordExtractConfig', 'systemPrompt']}
                    rules={[
                        {
                            required: enableKeywordExtract,
                            message: '请输入prompt',
                        },
                    ]}
                >
                    <AntInput.TextArea style={{ minHeight: 300 }} />
                </FormPro.Item>
                <FormPro.Item
                    label="具体所需信息"
                    rules={[
                        {
                            required: true,
                            message: '请选择具体所需信息',
                        },
                    ]}
                >
                    <IntentionLabelContainer
                        name={['keywordExtractConfig', 'paramConfig']}
                        form={form}
                        formFilters={[
                            'showName',
                            'name',
                            'dataType',
                            'tagPrompt',
                        ]}
                        allowBDChangeConfig={keywordAllowFrontLineChange}
                        allBtnText="新增所需抽取信息"
                        canEditByBD={keywordAllowFrontLineChange}
                    />
                </FormPro.Item>
            </div>
        </div>
    );
}
