.poi-statistics {
    padding: 16px 0px;

    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
    }

    &__container {
        display: flex;
        gap: 12px;

        >* {
            flex: 1;
            min-width: 0;
            background-color: #fff;
            display: flex;
            justify-content: center;
            border-radius: 4px;
            padding: 12px 12px 18px 12px;
        }
    }

    &__create {
        align-items: center;

        .create-task-btn {
            background: #333;
            border-color: #333;
            border-radius: 4px;
            font-size: 14px;
            height: 32px;
            padding: 0 16px;

            &:hover {
                background: #555;
                border-color: #555;
            }

            .anticon {
                font-size: 12px;
            }
        }
    }

    &__total {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .total-label {
            font-size: 14px;
            line-height: 1;
            font-weight: 500;
        }

        .total-value {
            font-size: 26px;
            font-weight: 600;
            color: #333;
            line-height: 1;
            margin-bottom: -8px;
        }
    }

    &__item {
        display: flex;
        gap: 10px;
        min-width: 120px;
        align-items: flex-end;
        height: 100%;
        position: relative;

        .item-header {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .item-title {
                font-size: 14px;
                color: #333;
                font-weight: 500;
            }

            .item-value {
                font-size: 26px;
                font-weight: 600;
                color: #333;
                margin-bottom: -8px;
            }
        }

        .item-progress {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex: 1;

            .ant-progress {
                margin: 0;

                .ant-progress-bg {
                    height: 6px !important;
                    border-radius: 3px;
                }

                .ant-progress-inner {
                    background-color: #f0f0f0;
                    border-radius: 3px;
                }
            }

            .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .progress-text {
                    font-size: 12px;

                    &:last-child {
                        color: #999;
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1200px) {
        &__container {
            flex-wrap: wrap;
            gap: 16px;
        }

        &__item {
            min-width: 100px;
        }
    }

    @media (max-width: 768px) {
        padding: 12px 16px;

        &__container {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }

        &__total {
            align-items: flex-start;
        }

        &__item {
            width: 100%;
        }
    }
}