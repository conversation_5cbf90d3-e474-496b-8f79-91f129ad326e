.subtask-list {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 120px;
        color: #8c8c8c;
    }

    &__empty {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 120px;

        .ant-empty {
            margin: 0;

            .ant-empty-description {
                font-size: 12px;
                color: #bfbfbf;
            }
        }
    }

    &__content {
        flex: 1;
        overflow-y: auto;
        padding-right: 4px;


        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 2px;

            &:hover {
                background: #bfbfbf;
            }
        }
    }

    &__footer {
        text-align: center;
        padding-top: 8px;
        border-top: 1px solid #f5f5f5;
        margin-top: 8px;
    }

    &__load-more {
        font-size: 12px;
        padding: 0;
        height: auto;
        color: #1890ff;

        &:hover {
            color: #40a9ff;
        }
    }
}

.subtask-item {
    border-bottom: 1px solid #f5f5f5;
    padding-bottom: 12px;
    margin-bottom: 12px;
    width: '70%';
    position: relative;

    &:last-child {
        border-bottom: none;
    }

    &__header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 6px;
    }

    &__title {
        margin-right: 8px;

        .ant-typography {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
            margin: 0;
            line-height: 1.4;
        }
    }

    &__content {
        font-size: 12px;
        width: 80%;

        >div {
            margin-bottom: 4px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .ant-typography {
            line-height: 1.3;
        }
    }

    &__agent,
    &__contact,
    &__time {
        .ant-typography {
            color: #8c8c8c;
        }
    }

    &__summary {
        margin-top: 6px;
        padding: 10px;
        color: #666;
        font-size: 12px;
        line-height: 18px;
        border-radius: 4px;
        background: linear-gradient(96deg, #F9F9FF 0%, #F7F9FF 49%, #FAF7FF);

        .ant-typography {
            color: #666;
            line-height: 1.4;
        }
    }

    // 新增的样式
    &__meta,
    &__task-info {
        display: flex;
        flex-wrap: wrap;
        gap: 4px 16px;
        margin-bottom: 6px;

        .ant-typography {
            color: #8c8c8c;
        }

    }

    &__task-info.important {
        .ant-typography {
            color: #222;
        }
    }

    &__result {
        margin-bottom: 6px;
    }

    &__failure {
        display: flex;
        flex-wrap: wrap;
        gap: 4px 16px;

        .ant-typography {
            color: #ff4d4f !important;
        }
    }

    &__success {
        display: flex;
        flex-wrap: wrap;
        gap: 4px 16px;

        .ant-typography {
            color: #8c8c8c;
        }
    }

    &__actions {
        margin-top: 8px;
        position: absolute;
        right: 0;
        bottom: 0;

        button {
            margin: 0 8px;
        }

        .ant-btn-link {
            color: #1890ff;
            padding: 0;

            &:hover {
                color: #40a9ff;
            }
        }
    }
}