.poi-list {
    min-height: 100vh;
    background-color: rgb(245, 247, 255);
    margin-left: -20px;


    &__filter {
        padding: 0 16px;
        position: relative;
        background-color: #fff;

        &__opeation {
            position: absolute;
            bottom: 28px;
            right: 22px;
        }
    }


    &__content {
        margin-bottom: 16px;
    }

    &__pagination {
        display: flex;
        justify-content: center;
        padding: 16px;
        position: fixed;
        bottom: 0px;
        width: 100%;
        background-color: rgb(245, 247, 255);
    }
}

.poi-card-list {
    padding: 0 0 40px 0;

    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        background: #fafafa;
        border-radius: 8px;
    }

    &__error {
        padding: 24px;
        background: #fff;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
    }

    &__list {
        .ant-list-item {
            padding: 0 0 16px 0;
            border: none;
        }
    }

    &__item {
        width: 100%;
    }
}