/**
 * 【新建任务】弹窗
 * 备注：step显隐逻辑，必须使用style.display样式控制，确保表单项虽然隐藏了，但form实例仍能拿到所有表单项的数据，各种数据联动、数据校验才能生效。
 */
import React, { useMemo, useState, useEffect } from 'react';
import {
    Form,
    Input,
    Select,
    DatePicker,
    Radio,
    message,
    App,
    FormInstance,
    Row,
    Button,
    Space,
    Steps,
    Typography,
    Upload,
    Switch,
    Flex,
} from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import ContactTag from './ContactTag';
import ReachContentFormItem from './ReachContentFormItem';
import useModalCallAi from './ModalCallAi';
import SelectTaskType from '@src/components/SelectTaskType';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import CardRadioGroup from '@src/components/CardRadioGroup';
import useObjectType from '@src/hooks/useObjectType';
import FormItemControl from '@src/components/formItemControl';
import {
    ReachMethodEnum,
    reachTargetTagOptions,
    reachTypeOptions,
    ReachTypeEnum,
} from '@src/constants';
import './index.scss';
import FileUploaderExcel from '@src/components/fileUploaderExcel';

const ModalContent = ({
    form,
    handleSubmit,
    handleCancel,
}: {
    form: FormInstance;
    handleSubmit: () => Promise<boolean>;
    handleCancel: () => void;
}) => {
    const { data: objectTypeEnum } = useObjectType();
    const contactObjectType = Form.useWatch('contactObjectType', form); // 【商家类型】实时数据
    const poiChooseType = Form.useWatch('poiChooseType', form); // 【选择商家】类型 实时数据
    const reachType = Form.useWatch('reachType', form); // 【触达类型】实时数据
    const contactType = Form.useWatch('contactType', form); // 【触达方式】实时数据
    const workTimeType = Form.useWatch(['workStrategyDto', 'type'], form); // 【触达时间】类型 实时数据
    const contactTitle = Form.useWatch('contactTitle', form); // 【触达方式】是"IM群发"时，【关联agent】标题 实时数据
    const contactContent = Form.useWatch('contactContent', form); // 【触达方式】是"IM群发"、"文本外呼"时，【关联agent】文本 实时数据
    const sceneId = Form.useWatch('sceneId', form); // 【触达类型】为"智能调度"时，【关联agent】选择的场景id 实时数据
    const startTime = Form.useWatch('startTime', form); // 开始时间 实时数据
    const endTime = Form.useWatch('endTime', form); // 结束时间 实时数据
    const enableEndTime = Form.useWatch('enableEndTime', form); // 是否设置截止时间 实时数据
    const bizId = Form.useWatch('bizId', form);
    const [callableTime, setCallableTime] = useState<string[]>([]);

    const currentObjectTypeEnum = objectTypeEnum?.find(
        v => v.objectType === contactObjectType,
    ); // 当前【商家类型】对应原始选项
    const contactTagOptions = currentObjectTypeEnum?.contactTag; // 当前【商家类型】对应商家tag，用于【选择商家】为"筛选"时，展示商家tag选项
    const contactTypeOptions = currentObjectTypeEnum?.contactType; // 当前【商家类型】对应【触达方式】可选项
    const kpPriorityOptions = currentObjectTypeEnum?.kpType; // 当前【商家类型】对应【触达对象】可选项

    const { openModal } = useModalCallAi();

    const fetchCallableTime = async () => {
        try {
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/callableTime/query',
                { bizId },
            );
            if (res.code === 0 && res.data?.callableTimeDtoList) {
                const list = res.data.callableTimeDtoList.map(
                    (t: any) => `${t.startTime}-${t.endTime}`,
                );
                setCallableTime(list);
            } else {
                setCallableTime([]);
            }
        } catch (error) {
            setCallableTime([]);
            console.log(error);
        }
    };

    useEffect(() => {
        if (!bizId) return;
        fetchCallableTime();
    }, [bizId]);

    // 开始时间禁用逻辑：不能晚于结束时间
    const disabledStartTime = (current: any) => {
        if (!endTime) return false;
        return current && current.isAfter(dayjs(endTime));
    };

    // 结束时间禁用逻辑：不能早于开始时间
    const disabledEndTime = (current: any) => {
        if (!startTime) return false;
        return current && current.isBefore(dayjs(startTime));
    };

    const [agent, setAgent] = useState<any>(null); // 【触达方式】是"智能外呼"时，【关联agent】选择的agent 实时数据
    const [loading, setLoading] = useState(false); // 是否在表单loading中
    const [currentStep, setCurrentStep] = useState(0); // 当前step索引

    // 处理文件下载
    const handleDownloadTemplate = async () => {
        try {
            const res = await fetch('/xianfu/api-v2/dove/template/download', {
                method: 'GET',
                headers: {
                    Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                },
            });

            if (!res.ok) {
                throw new Error('下载失败');
            }

            const blob = await res.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', '被叫号码导入模板.xlsx');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            message.error('下载模板失败');
        }
    };

    // 处理文件上传前的校验
    const beforeUpload = file => {
        const isXlsx =
            file.type ===
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        if (!isXlsx) {
            message.error('只能上传 .xlsx 格式的文件！');
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    return (
        <Form
            className="create-task-form"
            form={form}
            layout="horizontal"
            labelAlign="right"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
        >
            <Steps
                style={{ padding: '0 50px 20px' }}
                current={currentStep}
                items={[
                    {
                        title: '基础信息',
                    },
                    {
                        title: '触达配置',
                    },
                ]}
            />
            <div
                style={{
                    display: currentStep === 0 ? 'block' : 'none',
                    margin: '0 40px',
                }}
            >
                <Form.Item
                    name="taskName"
                    label="任务名称"
                    required
                    rules={[{ required: true, message: '请输入任务名称' }]}
                >
                    <Input placeholder="请输入" maxLength={50} />
                </Form.Item>
                <Form.Item
                    name="bizId"
                    label="业务线"
                    required
                    rules={[{ required: true, message: '请选择业务线' }]}
                >
                    <TeamSelect optionPath="/xianfu/api-v2/dove/staff/biz/query" />
                </Form.Item>
                <Form.Item
                    name="taskType"
                    label="任务类型"
                    required
                    rules={[{ required: true, message: '请选择任务类型' }]}
                >
                    <SelectTaskType />
                </Form.Item>
                <Form.Item
                    name="contactObjectType"
                    label="商家类型"
                    required
                    rules={[{ required: true, message: '请选择商家类型' }]}
                >
                    <Select
                        placeholder="请选择商家类型"
                        options={objectTypeEnum?.map?.(v => ({
                            label: v.name,
                            value: v.objectType,
                        }))}
                    />
                </Form.Item>
                {contactObjectType === 3 ? (
                    <Form.Item
                        label="被叫号码"
                        name="phoneFile"
                        rules={[
                            { required: true, message: '请上传被叫号码文件' },
                        ]}
                    >
                        <FormItemControl>
                            {(value, onChange) => {
                                return (
                                    <div>
                                        <Flex>
                                            <FileUploaderExcel
                                                onChange={onChange}
                                                value={value}
                                            ></FileUploaderExcel>
                                            <Button
                                                type="primary"
                                                icon={<DownloadOutlined />}
                                                onClick={handleDownloadTemplate}
                                            >
                                                下载模板
                                            </Button>
                                        </Flex>
                                        <br />
                                        <Typography.Text type="secondary">
                                            仅支持上传一个.xlsx文件，且数据不超过10万条
                                            <br />
                                            您即将通过上传:电话号码
                                            进行外呼，请确认模板类型正确，如因使用了错误的模板导致外呼事故，将对上传业务方进行外呼限制惩物
                                        </Typography.Text>
                                    </div>
                                );
                            }}
                        </FormItemControl>
                    </Form.Item>
                ) : (
                    <>
                        <Form.Item
                            name="poiChooseType"
                            label="选择商家"
                            required
                            rules={[
                                { required: true, message: '请选择商家类型' },
                            ]}
                        >
                            <Radio.Group
                                options={
                                    [
                                        contactTagOptions?.length
                                            ? FilterOption
                                            : null,
                                        ManualInputOption,
                                    ].filter(Boolean) as any
                                }
                            />
                        </Form.Item>
                        {poiChooseType === ContactObjectType.FILTER && (
                            <Form.Item
                                name="contactTag"
                                label="&nbsp;"
                                colon={false}
                                required={false}
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择商家tag',
                                    },
                                ]}
                            >
                                <ContactTag
                                    options={reachTargetTagOptions.filter(
                                        item =>
                                            contactTagOptions?.includes(
                                                item.value,
                                            ),
                                    )}
                                />
                            </Form.Item>
                        )}
                        {poiChooseType === ContactObjectType.MANUAL_INPUT && (
                            <Form.Item
                                name="contactObjectIdList"
                                label="&nbsp;"
                                colon={false}
                                required={false}
                                rules={[
                                    { required: true, message: '请输入商家ID' },
                                    {
                                        message:
                                            '请输入使用英文逗号分隔的商家ID',
                                        validator: (_, value) => {
                                            if (!value)
                                                return Promise.resolve();
                                            const ids = value
                                                .split(',')
                                                .map(id => id.trim());
                                            if (ids.length > 5000) {
                                                return Promise.reject(
                                                    '最多输入5千个商家ID',
                                                );
                                            }
                                            const isValid = ids.every(id =>
                                                /^\d+$/.test(id),
                                            );
                                            if (!isValid) {
                                                return Promise.reject(
                                                    '请输入有效的数字ID，并用英文逗号分隔',
                                                );
                                            }
                                            return Promise.resolve();
                                        },
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    placeholder="将商家ID用英文逗号分隔, 最多输入5千个商家 ID"
                                    rows={4}
                                />
                            </Form.Item>
                        )}
                    </>
                )}
            </div>
            <div style={{ display: currentStep === 1 ? 'block' : 'none' }}>
                <Form.Item
                    name="reachType"
                    label="触达类型"
                    required
                    rules={[{ required: true, message: '请选择触达类型' }]}
                >
                    <CardRadioGroup options={reachTypeOptions} />
                </Form.Item>
                {reachType === ReachTypeEnum.SINGLE_COMMUNICATION &&
                    contactTypeOptions?.length && (
                        <Form.Item
                            name="contactType"
                            label="触达方式"
                            required
                            rules={[
                                { required: true, message: '请选择触达方式' },
                            ]}
                        >
                            <Radio.Group
                                options={contactTypeOptions.map(v => {
                                    return {
                                        // @ts-ignore
                                        label: v.name,
                                        // @ts-ignore
                                        value: v.code,
                                    };
                                })}
                            />
                        </Form.Item>
                    )}
                {kpPriorityOptions?.length &&
                contactType !== ReachMethodEnum.IM_MASS_SEND ? (
                    <Form.Item
                        name="kpPriority"
                        label="触达对象"
                        required
                        rules={[{ required: true, message: '请选择触达对象' }]}
                    >
                        <Select
                            mode="multiple"
                            style={{ width: '100%' }}
                            placeholder="将按添加顺序依次触达"
                            options={kpPriorityOptions?.map(item => ({
                                label: item,
                                value: item,
                            }))}
                            maxTagCount="responsive"
                        />
                    </Form.Item>
                ) : null}
                <Form.Item
                    name={['workStrategyDto', 'type']}
                    label="触达时间"
                    tooltip="开始时间可能会因任务积压情况相当延后；结束时间到时后将自动停止剩余任务。如无特殊需要，结束时间可以设置长一些。"
                    required
                    rules={[{ required: true, message: '请选择触达时间' }]}
                >
                    <Radio.Group
                        options={
                            [
                                contactType !== ReachMethodEnum.IM_MASS_SEND
                                    ? {
                                          label: '延迟触达',
                                          value: WorkTimeType.CUSTOM,
                                      }
                                    : undefined,
                                { label: '立即触达', value: WorkTimeType.NOW },
                            ].filter(Boolean) as any
                        }
                    />
                </Form.Item>
                {workTimeType === WorkTimeType.CUSTOM &&
                !!callableTime.length ? (
                    <Form.Item
                        label="&nbsp;"
                        colon={false}
                        style={{ margin: '-20px 0 10px 0' }}
                    >
                        <Typography.Text type="secondary">
                            可外呼时间为：{callableTime?.join('，')}
                            。遇到封禁期会暂停执行
                        </Typography.Text>
                    </Form.Item>
                ) : null}
                {workTimeType === WorkTimeType.CUSTOM ? (
                    <>
                        <Form.Item
                            name="startTime"
                            label="开始时间"
                            required={true}
                            rules={[
                                { required: true, message: '请选择开始时间' },
                            ]}
                        >
                            <DatePicker
                                showTime
                                style={{ width: '100%' }}
                                placeholder="请输入"
                                disabledDate={disabledStartTime}
                            />
                        </Form.Item>
                        <Form.Item
                            name="enableEndTime"
                            label="是否设置截止时间"
                            valuePropName="checked"
                            tooltip="启用后，到期未完成的任务会停止执行"
                        >
                            <Switch />
                        </Form.Item>
                        {enableEndTime ? (
                            <Form.Item
                                name="endTime"
                                label="截止时间"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择截止时间',
                                    },
                                ]}
                            >
                                <DatePicker
                                    showTime
                                    style={{ width: '100%' }}
                                    placeholder="请输入"
                                    disabledDate={disabledEndTime}
                                />
                            </Form.Item>
                        ) : null}
                    </>
                ) : null}
                <ReachContentFormItem
                    form={form}
                    reachType={reachType}
                    contactType={contactType}
                    contactTitle={contactTitle}
                    contactContent={contactContent}
                    sceneId={sceneId}
                    setAgent={setAgent}
                />
            </div>
            <Row justify="end">
                <Space>
                    {agent ? (
                        <Button
                            type="link"
                            onClick={() =>
                                openModal({
                                    parameters: agent?.placeholder,
                                    agentId: agent?.id,
                                })
                            }
                        >
                            外呼给我试试
                        </Button>
                    ) : null}
                    {currentStep === 0 ? (
                        <>
                            <Button onClick={handleCancel} disabled={loading}>
                                取消
                            </Button>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    try {
                                        // 校验step1表单数据
                                        await form.validateFields([
                                            'taskName', // 【任务名称】
                                            'bizId', // 【业务线】
                                            'taskType', // 【任务类型】
                                            'contactObjectType', // 【商家类型】
                                            'poiChooseType', // 【选择商家】类型
                                            'contactTag', // 【选择商家】为"筛选"时，手动选择的商家tag
                                            'contactObjectIdList', // 【选择商家】为"手动录入"时，手动输入的商家ID
                                        ]);
                                        // 校验成功才允许跳到下一步
                                        setCurrentStep(1);
                                    } catch (error) {
                                        console.log(error);
                                        message.error(
                                            '表单校验失败，请检查红色报错信息',
                                        );
                                    }
                                }}
                            >
                                下一步
                            </Button>
                        </>
                    ) : null}
                    {currentStep === 1 ? (
                        <>
                            <Button onClick={() => setCurrentStep(0)}>
                                上一步
                            </Button>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    setLoading(true);
                                    await handleSubmit();
                                    setLoading(false);
                                }}
                                loading={loading}
                            >
                                确认
                            </Button>
                        </>
                    ) : null}
                </Space>
            </Row>
        </Form>
    );
};

interface CreateTaskRequest {
    taskName: string; // 【任务名称】
    bizId: number; // 【业务线】ID
    taskType: string; // 【任务类型】
    reachType: number; // 【触达类型】
    sceneId: number; // 【触达类型】为"智能调度"时，【关联agent】选择的场景id
    processId: number; // 【调度流程】ID
    poiDto: {
        contactObjectType: number; // 【商家类型】
        contactObjectIdList: number[]; // 【选择商家】为"手动录入"时，手动输入的商家ID
        contactTag: number; // 【选择商家】为"筛选"时，手动选择的商家tag
        kpPriority: string[]; // 【触达对象】
        phoneFile?: string; // 【选择商家】为"电话号码上传"时，上传文件的 S3 链接
    };
    channelDto: {
        contactType: number; // 【触达方式】
        agentId: number; // 【触达方式】是"智能外呼"时，【关联agent】选择的agent
        massSendTitle: string; // 【触达方式】是"IM群发"时，【关联agent】标题
        contactContent: string; // 【触达方式】是"IM群发"、"文本外呼"时，【关联agent】文本内容
        contactImages: string[]; // 【触达方式】为"IM群发"时，【关联agent】图片
    };
    workStrategyDto: {
        type: number; // 【触达时间】类型
        startTime?: number; // 【触达时间】为"延迟触达"时，选择时间开始
        endTime?: number; // 【触达时间】为"延迟触达"时，选择时间结束
    };
}

enum ContactObjectType {
    FILTER = '1', // 筛选
    MANUAL_INPUT = '2', // 手动录入
    PHONE_UPLOAD = '3', // 电话号码上传
}

enum WorkTimeType {
    CUSTOM = '1', // 自定义
    NOW = '2', // 立即
}

const FilterOption = { label: '筛选', value: ContactObjectType.FILTER };
const ManualInputOption = {
    label: '手动录入',
    value: ContactObjectType.MANUAL_INPUT,
};
const PHONE_UPLOAD_OPTION = {
    label: '电话号码上传',
    value: ContactObjectType.PHONE_UPLOAD,
};

const useModalCreateTask = (refresh = () => {}): (() => Promise<boolean>) => {
    const { modal } = App.useApp();
    const [form] = Form.useForm();

    const handleSubmit = async () => {
        // 校验
        try {
            await form.validateFields();
        } catch (error) {
            console.error(error);
            message.error('表单校验失败，请检查红色报错信息');
            return false;
        }
        // 调接口
        try {
            const values = form.getFieldsValue();
            const strategyType = values.workStrategyDto?.type; // 【触达时间】类型
            const poiChooseType = values.poiChooseType; // 【选择商家】类型
            const params: CreateTaskRequest = {
                taskName: values.taskName, // 【任务名称】
                bizId: values.bizId, // 【业务线】ID
                taskType: values.taskType, // 【任务类型】
                reachType: values.reachType, // 【触达类型】
                poiDto: {
                    contactObjectType: +values.contactObjectType, // 【商家类型】
                    contactObjectIdList:
                        poiChooseType === ContactObjectType.MANUAL_INPUT
                            ? values.contactObjectIdList
                                ? values.contactObjectIdList
                                      .split(',')
                                      .map(Number)
                                : []
                            : undefined, // 【选择商家】为"手动录入"时，手动输入的商家ID
                    contactTag: values.contactTag?.[0], // 【选择商家】为"筛选"时，手动选择的商家tag
                    kpPriority: values.kpPriority, // 【触达对象】
                    phoneFile:
                        poiChooseType === ContactObjectType.PHONE_UPLOAD
                            ? values.phoneFile
                            : undefined, // 【选择商家】为"电话号码上传"时，上传文件的 S3 链接
                },
                channelDto: {
                    contactType:
                        values.reachType ===
                        ReachTypeEnum.INTELLIGENT_SCHEDULING
                            ? ReachMethodEnum.INTELLIGENT_SCHEDULING_CALL
                            : values.contactType, // 【触达方式】
                    agentId:
                        values.contactType !== ReachMethodEnum.IM_MASS_SEND
                            ? values.agentId
                            : undefined, // 【触达方式】是"智能外呼"时，【关联agent】选择的agent
                    massSendTitle: values.contactTitle, // 【触达方式】是"IM群发"时，【关联agent】标题
                    contactContent: values.contactContent, // 【触达方式】是"IM群发"、"文本外呼"时，【关联agent】文本内容
                    contactImages:
                        values.images?.fileList.map(
                            (file: any) => file?.response?.data,
                        ) || undefined, // 【触达方式】为"IM群发"时，【关联agent】图片
                },
                sceneId:
                    values.reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING
                        ? values.sceneId
                        : undefined, // 【触达类型】为"智能调度"时，【关联agent】选择的场景id
                processId:
                    values.reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING
                        ? values.processId
                        : undefined, // 【触达类型】为"智能调度"时，【调度流程】选择的调度流程id
                workStrategyDto: {
                    type: strategyType, // 【触达时间】类型
                    startTime:
                        strategyType === WorkTimeType.CUSTOM && values.startTime
                            ? dayjs(values.startTime).valueOf()
                            : undefined, // 【触达时间】为"延迟触达"时，选择时间开始
                    endTime:
                        strategyType === WorkTimeType.CUSTOM &&
                        values.enableEndTime &&
                        values.endTime
                            ? dayjs(values.endTime).valueOf()
                            : undefined, // 【触达时间】为"延迟触达"时，选择时间结束
                },
            };

            // 0610新增逻辑，如果是选择触达类型为"智能调度"，则需要对参数额外处理
            if (values.reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
                delete params.sceneId;
                params.channelDto.agentId = values.sceneId;
            }

            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/task/create',
                params as any,
            );
            if (res.code !== 0) {
                return Promise.reject(new Error(res.msg || '创建任务失败'));
            }
            message.success('创建任务成功');
            refresh();
            form.resetFields();
            return true;
        } catch (error) {
            console.error('创建任务失败:', error);
            return false;
        }
    };

    const openModal = (): Promise<boolean> => {
        return new Promise(() => {
            form.resetFields();
            form.setFieldsValue({});

            const insWrapper: any = {};
            insWrapper.modalIns = modal.confirm({
                closable: true,
                title: '新建任务',
                width: 640,
                maskClosable: false,
                icon: null,
                content: (
                    <ModalContent
                        form={form}
                        handleSubmit={async () => {
                            try {
                                const result = await handleSubmit();
                                if (result) {
                                    insWrapper.modalIns?.destroy();
                                }
                                return result;
                            } catch (error) {
                                console.error('创建任务失败:', error);
                                return false;
                            }
                        }}
                        handleCancel={() => insWrapper.modalIns?.destroy()}
                    />
                ),
                footer: null,
            });
        });
    };

    return openModal;
};

export default useModalCreateTask;
