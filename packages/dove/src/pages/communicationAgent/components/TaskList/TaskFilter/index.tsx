import React, { useState, useEffect, useRef } from 'react';
import {
    Input,
    DatePicker,
    Button,
    Select as AntSelect,
    Form,
    Row,
    Col,
} from 'antd';
import type { SelectProps } from 'antd';
import SelectTaskType from '@src/components/SelectTaskType';
import MisSelect from '@src/components/MisSelector';
import OrganizationSelector from '@src/components/RooOrganizationSelector';
import SelectAgent from '@src/components/SelectAgent';
import ProcessSelect from '@src/components/ProcessSelect';
import { useOptionsByObjectType } from '@src/hooks/useObjectType';
import useAgentType from '@src/hooks/useAgentType';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import { filterReachMethodOptions, reachTypeOptions } from '@src/constants';
import {
    getAgentTaskIdFromUrl,
    removeAgentTaskIdFromUrl,
} from '@src/pages/communicationAgent/utils';
import './index.scss';

interface TaskFilterProps {
    onFilterChange: (filters: any) => void;
    onClear: () => void;
    showFilter: boolean;
}

const TaskFilter: React.FC<TaskFilterProps> = ({
    onFilterChange,
    onClear,
    showFilter,
}) => {
    const [form] = Form.useForm();
    const contactObjectType = Form.useWatch('contactObjectType', form); // 【商家/对象类型】实时数据
    const { agentTypeList } = useAgentType();
    const { objectTypeEnum, allUniqueKpTypes } = useOptionsByObjectType({
        contactObjectType,
    });
    const isInitialMount = useRef(true); // 跟踪是否是初始挂载

    // 初始化时设置外部传入的任务ID
    useEffect(() => {
        const agentTaskId = getAgentTaskIdFromUrl();
        if (agentTaskId && isInitialMount.current) {
            form.setFieldValue('id', getAgentTaskIdFromUrl());
            isInitialMount.current = false;
            // 触发查询
            const values = form.getFieldsValue();
            onFilterChange({ ...values, id: agentTaskId });
        }
    }, []);

    // 监听任务ID字段的变化，标记用户是否手动操作过id
    const handleTaskIdChange = () => {
        if (!isInitialMount.current) {
            removeAgentTaskIdFromUrl();
        }
    };

    // 【确定】按钮点击事件
    const handleConfirm = () => {
        const values = form.getFieldsValue();
        onFilterChange(values);
    };

    // 【清空选择】按钮点击事件
    const handleClear = () => {
        form.resetFields();
        handleTaskIdChange();
        onClear();
    };

    const onChangeObjectType = () => {
        form.setFieldValue('kpPriority', undefined);
        form.setFieldValue('contactType', undefined);
    };

    const formItems = [
        {
            name: 'id',
            label: '任务ID',
            component: (
                <Input
                    placeholder="请输入任务ID"
                    onChange={handleTaskIdChange}
                />
            ),
        },
        {
            name: 'taskType',
            label: '任务类型',
            component: (
                <SelectTaskType
                    mode="multiple"
                    maxTagCount="responsive"
                    placeholder="请选择任务类型"
                />
            ),
        },
        {
            name: 'agentId',
            label: '关联agent',
            component: (
                <SelectAgent
                    mode="multiple"
                    maxTagCount="responsive"
                    allowClear
                    placeholder="请选择关联agent"
                />
            ),
        },
        {
            name: 'agentType',
            label: '关联agent类型',
            component: (
                <Select
                    options={agentTypeList}
                    placeholder="请选择关联agent类型"
                />
            ),
        },
        {
            name: 'contactObjectType',
            label: '商家/对象类型',
            component: (
                <Select
                    options={(objectTypeEnum as any)?.map?.(v => ({
                        value: v.objectType,
                        label: v.name,
                    }))}
                    placeholder="请选择商家/对象类型"
                    onChange={onChangeObjectType}
                    mode={undefined}
                />
            ),
        },
        {
            name: 'reachType',
            label: '触达类型',
            component: (
                <Select
                    options={reachTypeOptions}
                    placeholder="请选择触达类型"
                    mode={undefined}
                />
            ),
        },
        {
            name: 'contactType',
            label: '触达方式',
            component: (
                <Select
                    options={filterReachMethodOptions}
                    placeholder="请选择触达方式"
                />
            ),
        },
        {
            name: 'creatorUid',
            label: '创建人',
            component: (
                <MisSelect
                    mis={false}
                    mode="multiple"
                    placeholder="请输入创建人"
                />
            ),
        },
        {
            name: 'createTimeRange',
            label: '任务创建时间',
            component: <DatePicker.RangePicker style={{ width: '100%' }} />,
        },
        {
            name: 'contactKp',
            label: '触达对象',
            component: (
                <Select
                    options={allUniqueKpTypes}
                    placeholder="请选择触达对象"
                />
            ),
        },
        {
            name: 'creatorOrgId',
            label: '创建人组织架构',
            component: <OrganizationSelector multiple />,
        },
        {
            name: 'touchTimeRange',
            label: '计划触达时间',
            component: <DatePicker.RangePicker style={{ width: '100%' }} />,
        },
        {
            name: 'processId',
            label: '调度流程名称/ID',
            component: (
                <ProcessSelect
                    params={{
                        pageNum: 1,
                        pageSize: 100000,
                    }}
                    searchAll={true}
                    mode="multiple"
                    maxTagCount="responsive"
                />
            ),
        },
        {
            name: 'bizId',
            label: '业务线租户',
            component: (
                <TeamSelect
                    optionPath="/xianfu/api-v2/dove/staff/biz/query"
                    mode="multiple"
                    maxTagCount="responsive"
                />
            ),
        },
        {
            name: 'contactObjectId',
            label: '商家ID',
            component: <Input placeholder="请输入商家ID" />,
        },
    ];

    return (
        <div
            className="task-filter"
            style={showFilter ? { display: 'block' } : { display: 'none' }}
        >
            <Form form={form} layout="vertical">
                <Row gutter={24}>
                    {formItems.map((item, index) => (
                        <Col span={8} key={index}>
                            <Form.Item name={item.name} label={item.label}>
                                {item.component}
                            </Form.Item>
                        </Col>
                    ))}
                </Row>
                <div className="task-filter__actions">
                    <Button
                        className="task-filter__button task-filter__button--clear"
                        onClick={handleClear}
                    >
                        清空选择
                    </Button>
                    <Button onClick={handleConfirm} type="primary">
                        确定
                    </Button>
                </div>
            </Form>
        </div>
    );
};

export default TaskFilter;

const Select: React.FC<SelectProps> = props => {
    return (
        <AntSelect
            style={{ width: '100%' }}
            mode="multiple"
            maxTagCount="responsive"
            allowClear
            {...props}
        />
    );
};
