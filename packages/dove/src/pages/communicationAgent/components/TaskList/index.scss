.task-list {
  padding: 20px;
  padding: 0px;
  padding-left: 0;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &__title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search-filter {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    position: relative;
    align-items: center;
  }

  &__search {
    flex: 1;
  }

  &__filter-panel {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    width: 940px;
    margin-top: 4px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &__tabs {
    margin-bottom: 20px;

    :global {
      .ant-tabs-nav {
        margin-bottom: 0;
      }

      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 13px;
      }

      .ant-tabs-ink-bar {
        background: #FFCD29;
        height: 2px;
      }

      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #000;
        font-weight: 500;
      }
    }
  }

  &__create-button {
    width: 100%;
    margin-bottom: 20px;
    font-size: 16px;
    align-items: center !important;
    gap: 8px;

    .anticon {
      font-size: 16px;
    }

    span {
      line-height: 1;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
    height: 0;
    min-height: 0;
  }

  &__load-more {
    text-align: center;
    padding: 16px 0;
    color: #8c8c8c;
    font-size: 14px;
  }

  &__loading,
  &__no-more {
    color: #8c8c8c;
    font-size: 14px;
  }
}

.task-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #FFCD29;
  }

  &--selected {
    border: 1px solid #FFCD29 !important;
    background-color: #FFFDF0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      right: -1px;
      top: 50%;
      transform: translate(100%, -50%);
      width: 0;
      height: 0;
      border-top: 7px solid transparent;
      border-bottom: 7px solid transparent;
      border-left: 7px solid #FFCD29;
    }

    &::after {
      content: '';
      position: absolute;
      right: 0px;
      top: 50%;
      transform: translate(100%, -50%);
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 6px solid #FFFDF0;
    }
  }

  .ant-progress-bg-outer {
    border-radius: 2px;
  }

  .ant-progress-inner {
    border-radius: 2px !important;
  }

  &--in-progress {
  }

  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    align-items: flex-start;
  }

  &__title {
    font-weight: bold;
    font-size: 16px;
    word-break: break-all;
  }

  &__status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;

    &--waiting {
      background-color: #F5F5F5;
      color: #8C8C8C;
    }

    &--in_progress {
      background-color: #FFF8E8;
      color: #FF8800;
    }

    &--completed {
      background-color: #F0F9F6;
      color: #52C41A;
    }
  }

  &__detail {
    display: flex;
    color: #8c8c8c;
    font-size: 12px;
    margin-top: 2px;
  }
  &__id{
    
  }

  &__detail-label {
    margin-right: 8px;
    white-space: pre;
  }

  &__detail-value {
    margin-right: 16px;
    color: #222;
  }
} 
