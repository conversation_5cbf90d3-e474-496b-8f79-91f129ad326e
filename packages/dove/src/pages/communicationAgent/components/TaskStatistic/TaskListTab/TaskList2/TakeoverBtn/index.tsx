/**
 * 【接管】按钮
 */
import React from 'react';
import { Button, App } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { TaskList2Data } from '../../../../../types';
import { ProcessStatusEnum } from '@src/constants';

interface Props {
    record: TaskList2Data; // 表格行数据
    onDone: () => void; // 操作成功后回调
}

const TakeoverBtn: React.FC<Props> = props => {
    const { record, onDone } = props;
    const { modal, message } = App.useApp();

    // 按钮点击事件
    const handleTakeover = () => {
        modal.confirm({
            title: '接管',
            content: (
                <div>
                    <div>确定由BD接管该任务吗？</div>
                    {record.objectName ? (
                        <div>
                            <span>门店信息：</span>
                            <span>{record.objectName || '-'}</span>
                        </div>
                    ) : null}
                </div>
            ),
            maskClosable: true,
            destroyOnClose: true,
            onOk: async () => {
                try {
                    const res: any = await apiCaller.post(
                        // @ts-ignore
                        '/xianfu/api-v2/dove/scheduling/takeover',
                        {
                            contactId: record.contactId,
                        },
                    );
                    if (res.code === 0) {
                        message.success('接管成功!');
                        // 刷新表格
                        onDone();
                        return Promise.resolve();
                    } else {
                        return Promise.reject(res.msg || '接管失败');
                    }
                } catch (err) {
                    return Promise.reject(err);
                }
            },
        });
    };

    return ![ProcessStatusEnum.BD_RECEIVE, ProcessStatusEnum.FAIL].includes(
        record.processStatus,
    ) ? (
        <Button type="link" onClick={handleTakeover}>
            接管
        </Button>
    ) : null;
};

export default TakeoverBtn;
